# This workflow uses actions that are not certified by GitHub.
# They are provided by a third-party and are governed by
# separate terms of service, privacy policy, and support
# documentation.

# GitHub recommends pinning actions to a commit SHA.
# To get a newer version, you will need to update the SHA.
# You can also reference a tag or branch, but the action may change without warning.

name: Create and publish a Docker image arm64

on:
  push:
    branches: ['master']
  workflow_dispatch:
  create:
env:
  DOCKERHUB_IMAGE_NAME: hanfangyuan/dify-on-wechat
  GITHUB_REGISTRY: ghcr.io
  GITHUB_IMAGE_NAME: hanfangyuan4396/dify-on-wechat
  ACR_REGISTRY: registry.cn-hangzhou.aliyuncs.com
  ACR_IMAGE_NAME: ${{ secrets.ACR_NAMESPACE }}/dify-on-wechat

jobs:
  build-and-push-image-arm64-dockerhub:
    if: github.repository == 'hanfangyuan4396/dify-on-wechat'
    runs-on: ubuntu-latest
    permissions:
      contents: read
      packages: write

    steps:
      - name: Checkout repository
        uses: actions/checkout@v3

      - name: Set up QEMU
        uses: docker/setup-qemu-action@v1

      - name: Set up Docker Buildx
        id: buildx
        uses: docker/setup-buildx-action@v1

      - name: Available platforms
        run: echo ${{ steps.buildx.outputs.platforms }}

      - name: Login to Docker Hub
        uses: docker/login-action@v2
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}

      - name: Extract metadata (tags, labels) for Docker
        id: meta
        uses: docker/metadata-action@v4
        with:
          images: |
            ${{ env.DOCKERHUB_IMAGE_NAME }}

      - name: Build and push Docker image
        uses: docker/build-push-action@v3
        with:
          context: .
          push: true
          file: ./docker/Dockerfile.latest
          platforms: linux/arm64
          tags: ${{ env.DOCKERHUB_IMAGE_NAME }}:arm64
          labels: ${{ steps.meta.outputs.labels }}

  build-and-push-image-arm64-ghcr:
    if: github.repository == 'hanfangyuan4396/dify-on-wechat'
    runs-on: ubuntu-latest
    permissions:
      contents: read
      packages: write

    steps:
      - name: Checkout repository
        uses: actions/checkout@v3

      - name: Set up QEMU
        uses: docker/setup-qemu-action@v1

      - name: Set up Docker Buildx
        id: buildx
        uses: docker/setup-buildx-action@v1

      - name: Available platforms
        run: echo ${{ steps.buildx.outputs.platforms }}

      - name: Log in to the Container registry
        uses: docker/login-action@v2
        with:
          registry: ${{ env.GITHUB_REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Extract metadata (tags, labels) for Docker
        id: meta
        uses: docker/metadata-action@v4
        with:
          images: |
            ${{ env.GITHUB_REGISTRY }}/${{ env.GITHUB_IMAGE_NAME }}

      - name: Build and push Docker image
        uses: docker/build-push-action@v3
        with:
          context: .
          push: true
          file: ./docker/Dockerfile.latest
          platforms: linux/arm64
          tags: ${{ env.GITHUB_REGISTRY }}/${{ env.GITHUB_IMAGE_NAME }}:arm64
          labels: ${{ steps.meta.outputs.labels }}

  build-and-push-image-arm64-acr:
    if: github.repository == 'hanfangyuan4396/dify-on-wechat'
    runs-on: ubuntu-latest
    permissions:
      contents: read
      packages: write

    steps:
      - name: Checkout repository
        uses: actions/checkout@v3

      - name: Set up QEMU
        uses: docker/setup-qemu-action@v1

      - name: Set up Docker Buildx
        id: buildx
        uses: docker/setup-buildx-action@v1

      - name: Available platforms
        run: echo ${{ steps.buildx.outputs.platforms }}

      - name: Login to the ACR
        uses: docker/login-action@v2
        with:
          registry: ${{ env.ACR_REGISTRY }}
          username: ${{ secrets.ACR_USERNAME }}
          password: ${{ secrets.ACR_PASSWORD }}

      - name: Extract metadata (tags, labels) for Docker
        id: meta
        uses: docker/metadata-action@v4
        with:
          images: |
            ${{ env.ACR_REGISTRY }}/${{ env.ACR_IMAGE_NAME }}

      - name: Build and push Docker image
        uses: docker/build-push-action@v3
        with:
          context: .
          push: true
          file: ./docker/Dockerfile.latest
          platforms: linux/arm64
          tags: ${{ env.ACR_REGISTRY }}/${{ env.ACR_IMAGE_NAME }}:arm64
          labels: ${{ steps.meta.outputs.labels }}
