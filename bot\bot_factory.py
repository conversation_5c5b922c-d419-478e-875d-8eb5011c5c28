"""
bot factory
"""
from common import const


def create_bot(bot_type):
    """
    create a bot_type instance
    :param bot_type: bot type code
    :return: bot instance
    """
    if bot_type == const.MOCK:
        from bot.mock.mock_bot import MockBot
        return MockBot()
    elif bot_type == "deepseek":
        from web_ui.deepseek_bot import DeepSeekBot
        return DeepSeekBot()

    raise RuntimeError(f"Unsupported bot type: {bot_type}")
