# 企业微信机器人 Web UI 使用指南

## 概述

本项目已成功改造为现代化的Web UI界面，提供了直观的图形化管理界面来控制和监控企业微信机器人。

## 功能特性

### 🎛️ 控制面板
- **启动/停止/暂停/恢复** 机器人服务
- **实时状态显示** 运行状态和运行时长
- **一键操作** 简单易用的控制按钮

### 📊 统计信息面板
- **消息统计** 接收消息数、回复消息数
- **成功率显示** 实时计算回复成功率
- **错误统计** 错误数量和类型统计
- **实时图表** 消息趋势可视化

### 📝 实时日志面板
- **实时日志流** 自动更新的日志显示
- **级别过滤** 支持DEBUG/INFO/WARNING/ERROR级别过滤
- **搜索功能** 快速查找特定日志内容
- **日志清空** 一键清空历史日志

### ⚙️ 配置管理面板
- **AI回复开关** 启用/禁用DeepSeek AI回复
- **API配置** DeepSeek API Key和Base URL设置
- **自定义回复** 设置固定回复内容
- **消息控制** 群聊/单聊消息开关
- **实时生效** 配置修改无需重启

### 💬 消息监控面板
- **实时消息流** 显示接收和发送的消息
- **消息分类** 按类型过滤消息（接收/发送/错误）
- **消息历史** 查看历史消息记录
- **消息详情** 显示发送者、接收者、时间等详细信息

## 安装和启动

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

新增的Web UI依赖包括：
- Flask>=2.3.0
- Flask-SocketIO>=5.3.0
- python-socketio>=5.8.0
- eventlet>=0.33.0

### 2. 启动方式

#### 方式一：同时启动机器人和Web UI（推荐）

```bash
python start_with_webui.py
```

可选参数：
- `--web-host`: Web UI监听地址（默认：0.0.0.0）
- `--web-port`: Web UI监听端口（默认：7860）
- `--debug`: 启用调试模式
- `--web-only`: 仅启动Web UI
- `--bot-only`: 仅启动机器人

#### 方式二：分别启动

启动机器人：
```bash
python app.py
```

启动Web UI：
```bash
python web_ui.py
```

### 3. 访问Web UI

启动成功后，在浏览器中访问：
```
http://localhost:7860
```

## 配置说明

### DeepSeek AI配置

1. 在Web UI的配置面板中启用"启用DeepSeek AI回复"
2. 输入您的DeepSeek API Key（格式：sk-xxx）
3. 设置API Base URL（默认：https://api.deepseek.com/v1）
4. 点击"测试API"验证配置
5. 保存配置

### 消息控制配置

- **启用群聊消息**：控制是否处理群聊消息
- **启用单聊消息**：控制是否处理单聊消息
- **自定义回复内容**：当未启用AI时使用的固定回复

### 配置文件

Web UI的配置会自动同步到 `config.json` 文件中，主要配置项：

```json
{
    "use_deepseek": false,
    "deepseek_api_key": "",
    "deepseek_api_base": "https://api.deepseek.com/v1",
    "deepseek_model": "deepseek-chat",
    "auto_reply_text": "我已经下班，有问题明天再说，急事请电联",
    "enable_group_chat": true,
    "enable_single_chat": true,
    "single_chat_prefix": [""]
}
```

## 使用流程

### 1. 首次使用

1. 启动服务：`python start_with_webui.py`
2. 访问Web UI：http://localhost:7860
3. 在配置面板中设置AI配置或自定义回复
4. 点击"启动"按钮启动机器人
5. 扫码登录企业微信

### 2. 日常使用

1. 通过控制面板管理机器人状态
2. 在统计面板查看运行数据
3. 在日志面板监控运行状态
4. 在消息面板查看消息收发情况
5. 在配置面板调整设置

### 3. 故障排查

1. 查看日志面板的错误信息
2. 检查配置是否正确
3. 确认网络连接正常
4. 验证API Key有效性

## API接口

Web UI提供了完整的RESTful API接口：

### 机器人控制
- `GET /api/status` - 获取机器人状态
- `POST /api/start` - 启动机器人
- `POST /api/stop` - 停止机器人
- `POST /api/pause` - 暂停机器人
- `POST /api/resume` - 恢复机器人

### 配置管理
- `GET /api/config` - 获取配置
- `PUT /api/config` - 更新配置
- `POST /api/config/validate` - 验证配置

### 统计信息
- `GET /api/statistics` - 获取统计数据
- `POST /api/statistics/reset` - 重置统计数据

### 日志管理
- `GET /api/logs` - 获取日志
- `DELETE /api/logs` - 清空日志

### 消息监控
- `GET /api/messages` - 获取消息记录
- `DELETE /api/messages` - 清空消息记录

## WebSocket事件

实时通信通过WebSocket实现：

- `status_update` - 状态更新
- `statistics_update` - 统计数据更新
- `log_message` - 新日志消息
- `message_received` - 接收到消息
- `message_sent` - 发送消息
- `config_changed` - 配置变更
- `notification` - 通知消息

## 测试

运行测试脚本验证Web UI功能：

```bash
python test_web_ui.py
```

测试内容包括：
- 连接测试
- API接口测试
- 功能完整性测试

## 注意事项

1. **端口占用**：确保7860端口未被占用
2. **防火墙**：如需外网访问，请开放相应端口
3. **API密钥**：DeepSeek API Key请妥善保管
4. **浏览器兼容性**：推荐使用Chrome、Firefox等现代浏览器
5. **网络连接**：确保服务器能访问DeepSeek API

## 故障排查

### 常见问题

1. **Web UI无法访问**
   - 检查服务是否正常启动
   - 确认端口是否被占用
   - 检查防火墙设置

2. **机器人无法启动**
   - 查看日志面板的错误信息
   - 检查企业微信登录状态
   - 确认配置文件正确

3. **AI回复不工作**
   - 验证DeepSeek API Key
   - 检查网络连接
   - 确认API配置正确

4. **消息无法接收**
   - 检查单聊/群聊开关
   - 确认前缀配置
   - 查看消息监控面板

### 日志分析

通过日志面板可以查看详细的运行日志：
- **DEBUG**：调试信息
- **INFO**：一般信息
- **WARNING**：警告信息
- **ERROR**：错误信息

## 技术架构

### 后端架构
- **Flask**: Web框架
- **Flask-SocketIO**: WebSocket支持
- **多线程**: 机器人服务和Web UI分离

### 前端架构
- **Bootstrap 5**: UI框架
- **Socket.IO**: 实时通信
- **Chart.js**: 数据可视化
- **原生JavaScript**: 交互逻辑

### 数据流
1. 企业微信消息 → WeworkChannel → 消息监控
2. 配置变更 → ConfigManager → 实时生效
3. 统计数据 → StatisticsManager → 实时图表
4. 日志信息 → LogManager → 实时显示

## 更新日志

### v1.0.0
- ✅ 完整的Web UI界面
- ✅ DeepSeek AI集成
- ✅ 实时消息监控
- ✅ 配置管理系统
- ✅ 统计数据可视化
- ✅ WebSocket实时通信

## 支持

如有问题或建议，请查看：
1. 日志面板的错误信息
2. 项目文档
3. GitHub Issues
