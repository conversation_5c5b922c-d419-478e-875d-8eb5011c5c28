# 最简单的Flask测试
from flask import Flask, jsonify
import logging

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)

@app.route('/')
def hello():
    return '''
    <html>
    <head><title>测试页面</title></head>
    <body>
        <h1>Flask服务正常运行！</h1>
        <p>如果您能看到这个页面，说明Flask已经正确安装和配置。</p>
        <a href="/api/test">测试API</a>
    </body>
    </html>
    '''

@app.route('/api/test')
def test_api():
    return jsonify({
        'success': True,
        'message': 'API测试成功！',
        'data': {
            'status': 'running',
            'version': '1.0.0'
        }
    })

if __name__ == '__main__':
    logger.info("启动Flask测试服务...")
    app.run(host='127.0.0.1', port=5000, debug=True)
