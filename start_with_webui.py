# 带Web UI的启动脚本
import os
import sys
import time
import threading
import argparse
import signal
import subprocess

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config import load_config, conf
from common.log import logger

class ServiceManager:
    """服务管理器"""
    
    def __init__(self):
        self.web_ui_process = None
        self.bot_thread = None
        self.should_stop = threading.Event()
        
    def start_web_ui(self, host='0.0.0.0', port=7860, debug=False):
        """启动Web UI服务"""
        try:
            logger.info("启动Web UI服务...")
            
            # 使用subprocess启动Web UI
            cmd = [
                sys.executable, 
                'web_ui.py',
                '--host', host,
                '--port', str(port)
            ]
            
            if debug:
                cmd.append('--debug')
            
            self.web_ui_process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            logger.info(f"Web UI服务已启动 (PID: {self.web_ui_process.pid})")
            logger.info(f"访问地址: http://{host}:{port}")
            
            return True
            
        except Exception as e:
            logger.error(f"启动Web UI服务失败: {e}")
            return False
    
    def start_bot(self):
        """启动机器人服务"""
        try:
            logger.info("启动机器人服务...")
            
            def bot_worker():
                try:
                    from app import run
                    run()
                except Exception as e:
                    logger.error(f"机器人服务异常: {e}")
            
            self.bot_thread = threading.Thread(target=bot_worker, daemon=True)
            self.bot_thread.start()
            
            logger.info("机器人服务已启动")
            return True
            
        except Exception as e:
            logger.error(f"启动机器人服务失败: {e}")
            return False
    
    def stop_services(self):
        """停止所有服务"""
        logger.info("正在停止服务...")
        
        # 停止Web UI
        if self.web_ui_process:
            try:
                self.web_ui_process.terminate()
                self.web_ui_process.wait(timeout=10)
                logger.info("Web UI服务已停止")
            except subprocess.TimeoutExpired:
                logger.warning("Web UI服务停止超时，强制终止")
                self.web_ui_process.kill()
            except Exception as e:
                logger.error(f"停止Web UI服务失败: {e}")
        
        # 设置停止标志
        self.should_stop.set()
        
        logger.info("所有服务已停止")
    
    def wait_for_services(self):
        """等待服务运行"""
        try:
            while not self.should_stop.is_set():
                # 检查Web UI进程状态
                if self.web_ui_process and self.web_ui_process.poll() is not None:
                    logger.error("Web UI服务意外退出")
                    break
                
                # 检查机器人线程状态
                if self.bot_thread and not self.bot_thread.is_alive():
                    logger.error("机器人服务意外退出")
                    break
                
                time.sleep(1)
                
        except KeyboardInterrupt:
            logger.info("收到中断信号")
        finally:
            self.stop_services()

def signal_handler(signum, frame):
    """信号处理器"""
    logger.info(f"收到信号 {signum}，正在关闭服务...")
    if hasattr(signal_handler, 'service_manager'):
        signal_handler.service_manager.stop_services()
    sys.exit(0)

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='企业微信机器人 + Web UI 启动器')
    parser.add_argument('--web-host', default='0.0.0.0', help='Web UI监听地址')
    parser.add_argument('--web-port', type=int, default=7860, help='Web UI监听端口')
    parser.add_argument('--debug', action='store_true', help='调试模式')
    parser.add_argument('--web-only', action='store_true', help='仅启动Web UI')
    parser.add_argument('--bot-only', action='store_true', help='仅启动机器人')
    
    args = parser.parse_args()
    
    try:
        # 加载配置
        load_config()
        
        # 创建服务管理器
        service_manager = ServiceManager()
        signal_handler.service_manager = service_manager
        
        # 注册信号处理器
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
        
        logger.info("企业微信机器人 + Web UI 启动器")
        logger.info("=" * 50)
        
        success = True
        
        # 启动Web UI
        if not args.bot_only:
            web_host = args.web_host or conf().get('web_ui_host', '0.0.0.0')
            web_port = args.web_port or conf().get('web_ui_port', 7860)
            
            if not service_manager.start_web_ui(web_host, web_port, args.debug):
                success = False
            else:
                # 等待Web UI启动
                time.sleep(3)
        
        # 启动机器人
        if not args.web_only and success:
            if not service_manager.start_bot():
                success = False
            else:
                # 等待机器人启动
                time.sleep(2)
        
        if success:
            logger.info("=" * 50)
            logger.info("所有服务启动成功！")
            
            if not args.bot_only:
                web_host = args.web_host or conf().get('web_ui_host', '0.0.0.0')
                web_port = args.web_port or conf().get('web_ui_port', 7860)
                logger.info(f"Web UI地址: http://{web_host}:{web_port}")
            
            if not args.web_only:
                logger.info("机器人服务已启动")
            
            logger.info("按 Ctrl+C 停止服务")
            logger.info("=" * 50)
            
            # 等待服务运行
            service_manager.wait_for_services()
        else:
            logger.error("服务启动失败")
            service_manager.stop_services()
            sys.exit(1)
            
    except Exception as e:
        logger.error(f"启动失败: {e}")
        logger.exception(e)
        sys.exit(1)

if __name__ == '__main__':
    main()
