# 完全独立的Web UI启动文件
import os
import sys
import logging
import argparse
from flask import Flask, render_template, jsonify, request
from flask_socketio import SocketIO, emit

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='[%(levelname)s][%(asctime)s] - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger(__name__)

def create_standalone_app():
    """创建独立的Flask应用"""
    # 确定模板和静态文件路径
    current_dir = os.path.dirname(os.path.abspath(__file__))
    template_dir = os.path.join(current_dir, 'web_ui', 'templates')
    static_dir = os.path.join(current_dir, 'web_ui', 'static')
    
    # 检查目录是否存在
    if not os.path.exists(template_dir):
        logger.warning(f"模板目录不存在: {template_dir}")
        template_dir = None
    
    if not os.path.exists(static_dir):
        logger.warning(f"静态文件目录不存在: {static_dir}")
        static_dir = None
    
    app = Flask(__name__, 
                template_folder=template_dir,
                static_folder=static_dir)
    app.config['SECRET_KEY'] = 'dify-on-wechat-secret-key-standalone'
    
    # 初始化SocketIO
    socketio = SocketIO(app, cors_allowed_origins="*", async_mode='threading')
    
    # 全局状态
    bot_status = {
        'status': 'stopped',
        'uptime': 0,
        'message_received': 0,
        'message_replied': 0,
        'success_rate': 0.0,
        'error_count': 0
    }
    
    config_data = {
        'use_deepseek': False,
        'deepseek_api_key': '',
        'deepseek_api_base': 'https://api.deepseek.com/v1',
        'auto_reply_text': '我已经下班，有问题明天再说，急事请电联',
        'enable_group_chat': True,
        'enable_single_chat': True
    }
    
    @app.route('/')
    def index():
        """主页面"""
        if template_dir and os.path.exists(os.path.join(template_dir, 'index.html')):
            return render_template('index.html')
        else:
            # 返回简单的HTML页面
            return '''
            <!DOCTYPE html>
            <html>
            <head>
                <title>Dify-on-WeChat Web UI</title>
                <meta charset="utf-8">
                <style>
                    body { font-family: Arial, sans-serif; margin: 40px; }
                    .container { max-width: 800px; margin: 0 auto; }
                    .status { padding: 20px; border: 1px solid #ddd; border-radius: 5px; margin: 20px 0; }
                    .button { padding: 10px 20px; margin: 5px; border: none; border-radius: 3px; cursor: pointer; }
                    .start { background-color: #4CAF50; color: white; }
                    .stop { background-color: #f44336; color: white; }
                    .pause { background-color: #ff9800; color: white; }
                </style>
            </head>
            <body>
                <div class="container">
                    <h1>Dify-on-WeChat Web UI</h1>
                    <div class="status">
                        <h3>机器人状态</h3>
                        <p>状态: <span id="status">已停止</span></p>
                        <p>运行时间: <span id="uptime">0</span> 秒</p>
                        <p>接收消息: <span id="received">0</span></p>
                        <p>回复消息: <span id="replied">0</span></p>
                    </div>
                    <div>
                        <button class="button start" onclick="controlBot('start')">启动</button>
                        <button class="button stop" onclick="controlBot('stop')">停止</button>
                        <button class="button pause" onclick="controlBot('pause')">暂停</button>
                        <button class="button start" onclick="controlBot('resume')">恢复</button>
                    </div>
                </div>
                
                <script>
                    function controlBot(action) {
                        fetch('/api/' + action, {method: 'POST'})
                            .then(response => response.json())
                            .then(data => {
                                if (data.success) {
                                    alert(data.message);
                                    updateStatus();
                                } else {
                                    alert('操作失败: ' + data.error);
                                }
                            });
                    }
                    
                    function updateStatus() {
                        fetch('/api/status')
                            .then(response => response.json())
                            .then(data => {
                                if (data.success) {
                                    const status = data.data;
                                    document.getElementById('status').textContent = 
                                        status.status === 'running' ? '运行中' :
                                        status.status === 'paused' ? '已暂停' : '已停止';
                                    document.getElementById('uptime').textContent = status.uptime;
                                    document.getElementById('received').textContent = status.message_received;
                                    document.getElementById('replied').textContent = status.message_replied;
                                }
                            });
                    }
                    
                    // 每5秒更新一次状态
                    setInterval(updateStatus, 5000);
                    updateStatus();
                </script>
            </body>
            </html>
            '''
    
    @app.route('/api/status', methods=['GET'])
    def get_status():
        return jsonify({'success': True, 'data': bot_status})
    
    @app.route('/api/start', methods=['POST'])
    def start_bot():
        bot_status['status'] = 'running'
        logger.info("机器人启动")
        return jsonify({'success': True, 'message': '机器人启动成功'})
    
    @app.route('/api/stop', methods=['POST'])
    def stop_bot():
        bot_status['status'] = 'stopped'
        logger.info("机器人停止")
        return jsonify({'success': True, 'message': '机器人停止成功'})
    
    @app.route('/api/pause', methods=['POST'])
    def pause_bot():
        bot_status['status'] = 'paused'
        logger.info("机器人暂停")
        return jsonify({'success': True, 'message': '机器人暂停成功'})
    
    @app.route('/api/resume', methods=['POST'])
    def resume_bot():
        bot_status['status'] = 'running'
        logger.info("机器人恢复")
        return jsonify({'success': True, 'message': '机器人恢复成功'})
    
    @app.route('/api/config', methods=['GET'])
    def get_config():
        return jsonify({'success': True, 'data': config_data})
    
    @app.route('/api/config', methods=['PUT'])
    def update_config():
        try:
            new_config = request.get_json()
            if new_config:
                config_data.update(new_config)
                logger.info(f"配置已更新: {new_config}")
                return jsonify({'success': True, 'message': '配置更新成功'})
            else:
                return jsonify({'success': False, 'error': '无效的配置数据'}), 400
        except Exception as e:
            logger.error(f"更新配置失败: {e}")
            return jsonify({'success': False, 'error': str(e)}), 500
    
    @socketio.on('connect')
    def handle_connect():
        logger.info("WebSocket客户端连接")
        emit('status_update', bot_status)
    
    @socketio.on('disconnect')
    def handle_disconnect():
        logger.info("WebSocket客户端断开连接")
    
    return app, socketio

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='独立Web UI')
    parser.add_argument('--host', default='127.0.0.1', help='监听地址')
    parser.add_argument('--port', type=int, default=7860, help='监听端口')
    parser.add_argument('--debug', action='store_true', help='调试模式')
    
    args = parser.parse_args()
    
    try:
        logger.info("启动独立Web UI...")
        
        app, socketio = create_standalone_app()
        
        logger.info(f"Web UI服务启动成功")
        logger.info(f"访问地址: http://{args.host}:{args.port}")
        logger.info("按 Ctrl+C 停止服务")
        
        socketio.run(
            app,
            host=args.host,
            port=args.port,
            debug=args.debug,
            allow_unsafe_werkzeug=True
        )
        
    except KeyboardInterrupt:
        logger.info("收到中断信号，正在关闭服务...")
    except Exception as e:
        logger.error(f"启动失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()
