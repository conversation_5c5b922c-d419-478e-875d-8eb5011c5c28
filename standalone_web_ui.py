# 完全独立的Web UI启动文件
import os
import sys
import logging
import argparse
from flask import Flask, render_template, jsonify, request
from flask_socketio import SocketIO, emit

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='[%(levelname)s][%(asctime)s] - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger(__name__)

def create_standalone_app():
    """创建独立的Flask应用"""
    # 确定模板和静态文件路径
    current_dir = os.path.dirname(os.path.abspath(__file__))
    template_dir = os.path.join(current_dir, 'web_ui', 'templates')
    static_dir = os.path.join(current_dir, 'web_ui', 'static')
    
    # 检查目录是否存在
    if not os.path.exists(template_dir):
        logger.warning(f"模板目录不存在: {template_dir}")
        template_dir = None
    
    if not os.path.exists(static_dir):
        logger.warning(f"静态文件目录不存在: {static_dir}")
        static_dir = None
    
    app = Flask(__name__, 
                template_folder=template_dir,
                static_folder=static_dir)
    app.config['SECRET_KEY'] = 'dify-on-wechat-secret-key-standalone'
    
    # 初始化SocketIO
    socketio = SocketIO(app, cors_allowed_origins="*", async_mode='threading')
    
    # 全局状态
    bot_status = {
        'status': 'stopped',
        'uptime': 0,
        'message_received': 0,
        'message_replied': 0,
        'success_rate': 0.0,
        'error_count': 0
    }
    
    config_data = {
        'use_deepseek': False,
        'deepseek_api_key': '',
        'deepseek_api_base': 'https://api.deepseek.com/v1',
        'auto_reply_text': '我已经下班，有问题明天再说，急事请电联',
        'enable_group_chat': True,
        'enable_single_chat': True
    }
    
    @app.route('/')
    def index():
        """主页面"""
        if template_dir and os.path.exists(os.path.join(template_dir, 'index.html')):
            return render_template('index.html')
        else:
            # 返回简单的HTML页面
            return '''
            <!DOCTYPE html>
            <html>
            <head>
                <title>Dify-on-WeChat Web UI</title>
                <meta charset="utf-8">
                <meta name="viewport" content="width=device-width, initial-scale=1">
                <style>
                    body {
                        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                        margin: 0; padding: 20px;
                        background-color: #f5f5f5;
                    }
                    .container {
                        max-width: 1200px;
                        margin: 0 auto;
                        background: white;
                        border-radius: 8px;
                        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                        overflow: hidden;
                    }
                    .header {
                        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                        color: white;
                        padding: 30px;
                        text-align: center;
                    }
                    .header h1 { margin: 0; font-size: 2.5em; }
                    .header p { margin: 10px 0 0 0; opacity: 0.9; }
                    .content { padding: 30px; }
                    .grid { display: grid; grid-template-columns: 1fr 1fr; gap: 30px; margin-bottom: 30px; }
                    .card {
                        background: #f8f9fa;
                        border: 1px solid #e9ecef;
                        border-radius: 8px;
                        padding: 25px;
                    }
                    .card h3 { margin-top: 0; color: #495057; }
                    .status-item {
                        display: flex;
                        justify-content: space-between;
                        margin: 15px 0;
                        padding: 10px;
                        background: white;
                        border-radius: 5px;
                        border-left: 4px solid #007bff;
                    }
                    .status-value { font-weight: bold; color: #007bff; }
                    .controls { text-align: center; margin: 30px 0; }
                    .button {
                        padding: 12px 25px;
                        margin: 8px;
                        border: none;
                        border-radius: 6px;
                        cursor: pointer;
                        font-size: 16px;
                        font-weight: 500;
                        transition: all 0.3s ease;
                        min-width: 100px;
                    }
                    .button:hover { transform: translateY(-2px); box-shadow: 0 4px 12px rgba(0,0,0,0.15); }
                    .start { background-color: #28a745; color: white; }
                    .stop { background-color: #dc3545; color: white; }
                    .pause { background-color: #ffc107; color: #212529; }
                    .config-section { margin-top: 30px; }
                    .form-group { margin: 20px 0; }
                    .form-group label { display: block; margin-bottom: 8px; font-weight: 500; color: #495057; }
                    .form-group input, .form-group textarea {
                        width: 100%;
                        padding: 12px;
                        border: 1px solid #ced4da;
                        border-radius: 4px;
                        font-size: 14px;
                        box-sizing: border-box;
                    }
                    .form-group input[type="checkbox"] { width: auto; margin-right: 8px; }
                    .tabs { display: flex; border-bottom: 2px solid #e9ecef; margin-bottom: 20px; }
                    .tab {
                        padding: 12px 24px;
                        cursor: pointer;
                        border-bottom: 2px solid transparent;
                        transition: all 0.3s ease;
                    }
                    .tab.active { border-bottom-color: #007bff; color: #007bff; font-weight: 500; }
                    .tab-content { display: none; }
                    .tab-content.active { display: block; }
                    .log-container {
                        background: #f8f9fa;
                        border: 1px solid #e9ecef;
                        border-radius: 4px;
                        height: 300px;
                        overflow-y: auto;
                        padding: 15px;
                        font-family: 'Courier New', monospace;
                        font-size: 13px;
                    }
                    .log-entry { margin: 5px 0; padding: 5px; border-radius: 3px; }
                    .log-info { background-color: #d1ecf1; color: #0c5460; }
                    .log-warning { background-color: #fff3cd; color: #856404; }
                    .log-error { background-color: #f8d7da; color: #721c24; }
                    @media (max-width: 768px) {
                        .grid { grid-template-columns: 1fr; }
                        .container { margin: 10px; }
                        .content { padding: 20px; }
                    }
                </style>
            </head>
            <body>
                <div class="container">
                    <div class="header">
                        <h1>Dify-on-WeChat</h1>
                        <p>企业微信机器人管理控制台</p>
                    </div>
                    <div class="content">
                        <div class="grid">
                            <div class="card">
                                <h3>🤖 机器人状态</h3>
                                <div class="status-item">
                                    <span>运行状态</span>
                                    <span class="status-value" id="status">已停止</span>
                                </div>
                                <div class="status-item">
                                    <span>运行时间</span>
                                    <span class="status-value" id="uptime">0 秒</span>
                                </div>
                                <div class="status-item">
                                    <span>接收消息</span>
                                    <span class="status-value" id="received">0</span>
                                </div>
                                <div class="status-item">
                                    <span>回复消息</span>
                                    <span class="status-value" id="replied">0</span>
                                </div>
                                <div class="status-item">
                                    <span>成功率</span>
                                    <span class="status-value" id="success-rate">0%</span>
                                </div>
                            </div>
                            <div class="card">
                                <h3>⚡ 快速操作</h3>
                                <div class="controls">
                                    <button class="button start" onclick="controlBot('start')">🚀 启动</button>
                                    <button class="button stop" onclick="controlBot('stop')">⏹️ 停止</button>
                                    <button class="button pause" onclick="controlBot('pause')">⏸️ 暂停</button>
                                    <button class="button start" onclick="controlBot('resume')">▶️ 恢复</button>
                                </div>
                                <div style="margin-top: 20px; text-align: center;">
                                    <button class="button" style="background-color: #6c757d; color: white;" onclick="refreshData()">🔄 刷新数据</button>
                                </div>
                            </div>
                        </div>

                        <div class="tabs">
                            <div class="tab active" onclick="switchTab('config')">⚙️ 配置管理</div>
                            <div class="tab" onclick="switchTab('logs')">📋 运行日志</div>
                            <div class="tab" onclick="switchTab('messages')">💬 消息记录</div>
                        </div>

                        <div id="config-tab" class="tab-content active">
                            <div class="card">
                                <h3>配置设置</h3>
                                <div class="form-group">
                                    <label>
                                        <input type="checkbox" id="use_deepseek"> 启用DeepSeek AI
                                    </label>
                                </div>
                                <div class="form-group">
                                    <label for="deepseek_api_key">DeepSeek API Key:</label>
                                    <input type="password" id="deepseek_api_key" placeholder="输入您的API Key">
                                </div>
                                <div class="form-group">
                                    <label for="auto_reply_text">自动回复文本:</label>
                                    <textarea id="auto_reply_text" rows="3" placeholder="设置自动回复内容"></textarea>
                                </div>
                                <div class="form-group">
                                    <label>
                                        <input type="checkbox" id="enable_group_chat"> 启用群聊功能
                                    </label>
                                </div>
                                <div class="form-group">
                                    <label>
                                        <input type="checkbox" id="enable_single_chat"> 启用单聊功能
                                    </label>
                                </div>
                                <button class="button start" onclick="saveConfig()">💾 保存配置</button>
                            </div>
                        </div>

                        <div id="logs-tab" class="tab-content">
                            <div class="card">
                                <h3>运行日志</h3>
                                <div class="log-container" id="log-container">
                                    <div class="log-entry log-info">[INFO] Web UI启动成功</div>
                                    <div class="log-entry log-info">[INFO] 等待机器人连接...</div>
                                </div>
                                <div style="margin-top: 15px;">
                                    <button class="button" style="background-color: #6c757d; color: white;" onclick="refreshLogs()">🔄 刷新日志</button>
                                    <button class="button" style="background-color: #dc3545; color: white;" onclick="clearLogs()">🗑️ 清空日志</button>
                                </div>
                            </div>
                        </div>

                        <div id="messages-tab" class="tab-content">
                            <div class="card">
                                <h3>消息记录</h3>
                                <div class="log-container" id="message-container">
                                    <div class="log-entry log-info">[接收] 测试用户: 测试消息</div>
                                </div>
                                <div style="margin-top: 15px;">
                                    <button class="button" style="background-color: #6c757d; color: white;" onclick="refreshMessages()">🔄 刷新消息</button>
                                    <button class="button" style="background-color: #dc3545; color: white;" onclick="clearMessages()">🗑️ 清空消息</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <script>
                    // 全局变量
                    let currentConfig = {};

                    // 控制机器人
                    function controlBot(action) {
                        fetch('/api/' + action, {method: 'POST'})
                            .then(response => response.json())
                            .then(data => {
                                if (data.success) {
                                    showNotification(data.message, 'success');
                                    updateStatus();
                                } else {
                                    showNotification('操作失败: ' + data.error, 'error');
                                }
                            })
                            .catch(error => {
                                showNotification('网络错误: ' + error.message, 'error');
                            });
                    }

                    // 更新状态
                    function updateStatus() {
                        fetch('/api/status')
                            .then(response => response.json())
                            .then(data => {
                                if (data.success) {
                                    const status = data.data;
                                    document.getElementById('status').textContent =
                                        status.status === 'running' ? '🟢 运行中' :
                                        status.status === 'paused' ? '🟡 已暂停' : '🔴 已停止';
                                    document.getElementById('uptime').textContent = status.uptime + ' 秒';
                                    document.getElementById('received').textContent = status.message_received;
                                    document.getElementById('replied').textContent = status.message_replied;
                                    document.getElementById('success-rate').textContent = status.success_rate + '%';
                                }
                            })
                            .catch(error => console.error('更新状态失败:', error));
                    }

                    // 切换标签页
                    function switchTab(tabName) {
                        // 隐藏所有标签内容
                        document.querySelectorAll('.tab-content').forEach(content => {
                            content.classList.remove('active');
                        });

                        // 移除所有标签的active类
                        document.querySelectorAll('.tab').forEach(tab => {
                            tab.classList.remove('active');
                        });

                        // 显示选中的标签内容
                        document.getElementById(tabName + '-tab').classList.add('active');

                        // 添加active类到选中的标签
                        event.target.classList.add('active');

                        // 根据标签加载相应数据
                        if (tabName === 'config') {
                            loadConfig();
                        } else if (tabName === 'logs') {
                            refreshLogs();
                        } else if (tabName === 'messages') {
                            refreshMessages();
                        }
                    }

                    // 加载配置
                    function loadConfig() {
                        fetch('/api/config')
                            .then(response => response.json())
                            .then(data => {
                                if (data.success) {
                                    currentConfig = data.data;
                                    document.getElementById('use_deepseek').checked = currentConfig.use_deepseek || false;
                                    document.getElementById('deepseek_api_key').value = currentConfig.deepseek_api_key || '';
                                    document.getElementById('auto_reply_text').value = currentConfig.auto_reply_text || '';
                                    document.getElementById('enable_group_chat').checked = currentConfig.enable_group_chat || false;
                                    document.getElementById('enable_single_chat').checked = currentConfig.enable_single_chat || false;
                                }
                            })
                            .catch(error => console.error('加载配置失败:', error));
                    }

                    // 保存配置
                    function saveConfig() {
                        const config = {
                            use_deepseek: document.getElementById('use_deepseek').checked,
                            deepseek_api_key: document.getElementById('deepseek_api_key').value,
                            auto_reply_text: document.getElementById('auto_reply_text').value,
                            enable_group_chat: document.getElementById('enable_group_chat').checked,
                            enable_single_chat: document.getElementById('enable_single_chat').checked
                        };

                        fetch('/api/config', {
                            method: 'PUT',
                            headers: {'Content-Type': 'application/json'},
                            body: JSON.stringify(config)
                        })
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) {
                                showNotification('配置保存成功', 'success');
                                currentConfig = config;
                            } else {
                                showNotification('配置保存失败: ' + data.error, 'error');
                            }
                        })
                        .catch(error => {
                            showNotification('网络错误: ' + error.message, 'error');
                        });
                    }

                    // 刷新日志
                    function refreshLogs() {
                        fetch('/api/logs')
                            .then(response => response.json())
                            .then(data => {
                                if (data.success) {
                                    const container = document.getElementById('log-container');
                                    container.innerHTML = '';
                                    data.data.forEach(log => {
                                        const entry = document.createElement('div');
                                        entry.className = 'log-entry log-' + log.level.toLowerCase();
                                        entry.textContent = `[${log.level}] ${log.formatted_time} - ${log.message}`;
                                        container.appendChild(entry);
                                    });
                                    container.scrollTop = container.scrollHeight;
                                }
                            })
                            .catch(error => console.error('刷新日志失败:', error));
                    }

                    // 刷新消息
                    function refreshMessages() {
                        fetch('/api/messages')
                            .then(response => response.json())
                            .then(data => {
                                if (data.success) {
                                    const container = document.getElementById('message-container');
                                    container.innerHTML = '';
                                    data.data.forEach(msg => {
                                        const entry = document.createElement('div');
                                        entry.className = 'log-entry log-info';
                                        entry.textContent = `[${msg.type === 'received' ? '接收' : '发送'}] ${msg.formatted_time} - ${msg.sender}: ${msg.content}`;
                                        container.appendChild(entry);
                                    });
                                    container.scrollTop = container.scrollHeight;
                                }
                            })
                            .catch(error => console.error('刷新消息失败:', error));
                    }

                    // 清空日志
                    function clearLogs() {
                        if (confirm('确定要清空所有日志吗？')) {
                            document.getElementById('log-container').innerHTML = '<div class="log-entry log-info">[INFO] 日志已清空</div>';
                        }
                    }

                    // 清空消息
                    function clearMessages() {
                        if (confirm('确定要清空所有消息记录吗？')) {
                            document.getElementById('message-container').innerHTML = '<div class="log-entry log-info">[INFO] 消息记录已清空</div>';
                        }
                    }

                    // 刷新所有数据
                    function refreshData() {
                        updateStatus();
                        loadConfig();
                        refreshLogs();
                        refreshMessages();
                        showNotification('数据已刷新', 'success');
                    }

                    // 显示通知
                    function showNotification(message, type = 'info') {
                        // 创建通知元素
                        const notification = document.createElement('div');
                        notification.style.cssText = `
                            position: fixed;
                            top: 20px;
                            right: 20px;
                            padding: 15px 20px;
                            border-radius: 5px;
                            color: white;
                            font-weight: 500;
                            z-index: 1000;
                            animation: slideIn 0.3s ease;
                            max-width: 300px;
                            word-wrap: break-word;
                        `;

                        // 根据类型设置颜色
                        switch(type) {
                            case 'success':
                                notification.style.backgroundColor = '#28a745';
                                break;
                            case 'error':
                                notification.style.backgroundColor = '#dc3545';
                                break;
                            case 'warning':
                                notification.style.backgroundColor = '#ffc107';
                                notification.style.color = '#212529';
                                break;
                            default:
                                notification.style.backgroundColor = '#007bff';
                        }

                        notification.textContent = message;
                        document.body.appendChild(notification);

                        // 3秒后自动移除
                        setTimeout(() => {
                            notification.style.animation = 'slideOut 0.3s ease';
                            setTimeout(() => {
                                if (notification.parentNode) {
                                    notification.parentNode.removeChild(notification);
                                }
                            }, 300);
                        }, 3000);
                    }

                    // 添加CSS动画
                    const style = document.createElement('style');
                    style.textContent = `
                        @keyframes slideIn {
                            from { transform: translateX(100%); opacity: 0; }
                            to { transform: translateX(0); opacity: 1; }
                        }
                        @keyframes slideOut {
                            from { transform: translateX(0); opacity: 1; }
                            to { transform: translateX(100%); opacity: 0; }
                        }
                    `;
                    document.head.appendChild(style);

                    // 页面加载完成后初始化
                    document.addEventListener('DOMContentLoaded', function() {
                        updateStatus();
                        loadConfig();

                        // 每5秒更新一次状态
                        setInterval(updateStatus, 5000);

                        showNotification('Web UI已就绪', 'success');
                    });
                </script>
            </body>
            </html>
            '''
    
    @app.route('/api/status', methods=['GET'])
    def get_status():
        return jsonify({'success': True, 'data': bot_status})
    
    @app.route('/api/start', methods=['POST'])
    def start_bot():
        bot_status['status'] = 'running'
        logger.info("机器人启动")
        return jsonify({'success': True, 'message': '机器人启动成功'})
    
    @app.route('/api/stop', methods=['POST'])
    def stop_bot():
        bot_status['status'] = 'stopped'
        logger.info("机器人停止")
        return jsonify({'success': True, 'message': '机器人停止成功'})
    
    @app.route('/api/pause', methods=['POST'])
    def pause_bot():
        bot_status['status'] = 'paused'
        logger.info("机器人暂停")
        return jsonify({'success': True, 'message': '机器人暂停成功'})
    
    @app.route('/api/resume', methods=['POST'])
    def resume_bot():
        bot_status['status'] = 'running'
        logger.info("机器人恢复")
        return jsonify({'success': True, 'message': '机器人恢复成功'})
    
    @app.route('/api/config', methods=['GET'])
    def get_config():
        return jsonify({'success': True, 'data': config_data})
    
    @app.route('/api/config', methods=['PUT'])
    def update_config():
        try:
            new_config = request.get_json()
            if new_config:
                config_data.update(new_config)
                logger.info(f"配置已更新: {new_config}")
                socketio.emit('config_update', config_data)
                return jsonify({'success': True, 'message': '配置更新成功'})
            else:
                return jsonify({'success': False, 'error': '无效的配置数据'}), 400
        except Exception as e:
            logger.error(f"更新配置失败: {e}")
            return jsonify({'success': False, 'error': str(e)}), 500

    @app.route('/api/logs', methods=['GET'])
    def get_logs():
        """获取日志"""
        # 模拟日志数据
        import datetime
        now = datetime.datetime.now()
        logs = [
            {
                'level': 'INFO',
                'message': 'Web UI启动成功',
                'timestamp': now.isoformat(),
                'formatted_time': now.strftime('%Y-%m-%d %H:%M:%S')
            },
            {
                'level': 'INFO',
                'message': f'当前状态: {bot_status["status"]}',
                'timestamp': now.isoformat(),
                'formatted_time': now.strftime('%Y-%m-%d %H:%M:%S')
            }
        ]
        return jsonify({'success': True, 'data': logs})

    @app.route('/api/statistics', methods=['GET'])
    def get_statistics():
        """获取统计数据"""
        return jsonify({'success': True, 'data': bot_status})

    @app.route('/api/messages', methods=['GET'])
    def get_messages():
        """获取消息记录"""
        # 模拟消息数据
        import datetime
        now = datetime.datetime.now()
        messages = [
            {
                'type': 'received',
                'content': '测试消息',
                'sender': '测试用户',
                'timestamp': now.isoformat(),
                'formatted_time': now.strftime('%Y-%m-%d %H:%M:%S')
            }
        ]
        return jsonify({'success': True, 'data': messages})
    
    @socketio.on('connect')
    def handle_connect():
        logger.info("WebSocket客户端连接")
        emit('status_update', bot_status)
    
    @socketio.on('disconnect')
    def handle_disconnect():
        logger.info("WebSocket客户端断开连接")
    
    return app, socketio

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='独立Web UI')
    parser.add_argument('--host', default='127.0.0.1', help='监听地址')
    parser.add_argument('--port', type=int, default=7860, help='监听端口')
    parser.add_argument('--debug', action='store_true', help='调试模式')
    
    args = parser.parse_args()
    
    try:
        logger.info("启动独立Web UI...")
        
        app, socketio = create_standalone_app()
        
        logger.info(f"Web UI服务启动成功")
        logger.info(f"访问地址: http://{args.host}:{args.port}")
        logger.info("按 Ctrl+C 停止服务")
        
        socketio.run(
            app,
            host=args.host,
            port=args.port,
            debug=args.debug,
            allow_unsafe_werkzeug=True
        )
        
    except KeyboardInterrupt:
        logger.info("收到中断信号，正在关闭服务...")
    except Exception as e:
        logger.error(f"启动失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()
