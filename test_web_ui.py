# Web UI测试脚本
import os
import sys
import time
import requests
import json
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

class WebUITester:
    """Web UI测试类"""
    
    def __init__(self, base_url="http://localhost:7860"):
        self.base_url = base_url
        self.session = requests.Session()
        
    def test_connection(self):
        """测试连接"""
        try:
            response = self.session.get(f"{self.base_url}/")
            if response.status_code == 200:
                print("✓ Web UI连接成功")
                return True
            else:
                print(f"✗ Web UI连接失败: {response.status_code}")
                return False
        except Exception as e:
            print(f"✗ Web UI连接异常: {e}")
            return False
    
    def test_api_status(self):
        """测试状态API"""
        try:
            response = self.session.get(f"{self.base_url}/api/status")
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    print("✓ 状态API测试成功")
                    print(f"  当前状态: {data['data'].get('status', 'unknown')}")
                    return True
                else:
                    print(f"✗ 状态API返回错误: {data.get('error', 'unknown')}")
                    return False
            else:
                print(f"✗ 状态API请求失败: {response.status_code}")
                return False
        except Exception as e:
            print(f"✗ 状态API测试异常: {e}")
            return False
    
    def test_api_config(self):
        """测试配置API"""
        try:
            # 测试获取配置
            response = self.session.get(f"{self.base_url}/api/config")
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    print("✓ 配置获取API测试成功")
                    config = data['data']
                    
                    # 测试更新配置
                    test_config = {
                        "auto_reply_text": f"测试回复 - {datetime.now().strftime('%H:%M:%S')}"
                    }
                    
                    response = self.session.put(
                        f"{self.base_url}/api/config",
                        json=test_config,
                        headers={'Content-Type': 'application/json'}
                    )
                    
                    if response.status_code == 200:
                        data = response.json()
                        if data.get('success'):
                            print("✓ 配置更新API测试成功")
                            return True
                        else:
                            print(f"✗ 配置更新API返回错误: {data.get('error', 'unknown')}")
                            return False
                    else:
                        print(f"✗ 配置更新API请求失败: {response.status_code}")
                        return False
                else:
                    print(f"✗ 配置获取API返回错误: {data.get('error', 'unknown')}")
                    return False
            else:
                print(f"✗ 配置获取API请求失败: {response.status_code}")
                return False
        except Exception as e:
            print(f"✗ 配置API测试异常: {e}")
            return False
    
    def test_api_statistics(self):
        """测试统计API"""
        try:
            response = self.session.get(f"{self.base_url}/api/statistics")
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    print("✓ 统计API测试成功")
                    stats = data['data']
                    print(f"  接收消息: {stats.get('message_received', 0)}")
                    print(f"  回复消息: {stats.get('message_replied', 0)}")
                    print(f"  成功率: {stats.get('success_rate', 0)}%")
                    return True
                else:
                    print(f"✗ 统计API返回错误: {data.get('error', 'unknown')}")
                    return False
            else:
                print(f"✗ 统计API请求失败: {response.status_code}")
                return False
        except Exception as e:
            print(f"✗ 统计API测试异常: {e}")
            return False
    
    def test_api_logs(self):
        """测试日志API"""
        try:
            response = self.session.get(f"{self.base_url}/api/logs?limit=10")
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    print("✓ 日志API测试成功")
                    logs = data['data']
                    print(f"  获取到 {len(logs)} 条日志")
                    return True
                else:
                    print(f"✗ 日志API返回错误: {data.get('error', 'unknown')}")
                    return False
            else:
                print(f"✗ 日志API请求失败: {response.status_code}")
                return False
        except Exception as e:
            print(f"✗ 日志API测试异常: {e}")
            return False
    
    def test_api_messages(self):
        """测试消息API"""
        try:
            response = self.session.get(f"{self.base_url}/api/messages?limit=10")
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    print("✓ 消息API测试成功")
                    messages = data['data']
                    print(f"  获取到 {len(messages)} 条消息")
                    return True
                else:
                    print(f"✗ 消息API返回错误: {data.get('error', 'unknown')}")
                    return False
            else:
                print(f"✗ 消息API请求失败: {response.status_code}")
                return False
        except Exception as e:
            print(f"✗ 消息API测试异常: {e}")
            return False
    
    def run_all_tests(self):
        """运行所有测试"""
        print("开始Web UI功能测试...")
        print("=" * 50)
        
        tests = [
            ("连接测试", self.test_connection),
            ("状态API测试", self.test_api_status),
            ("配置API测试", self.test_api_config),
            ("统计API测试", self.test_api_statistics),
            ("日志API测试", self.test_api_logs),
            ("消息API测试", self.test_api_messages),
        ]
        
        passed = 0
        total = len(tests)
        
        for test_name, test_func in tests:
            print(f"\n{test_name}:")
            try:
                if test_func():
                    passed += 1
            except Exception as e:
                print(f"✗ {test_name}异常: {e}")
        
        print("\n" + "=" * 50)
        print(f"测试完成: {passed}/{total} 通过")
        
        if passed == total:
            print("🎉 所有测试通过！")
            return True
        else:
            print("❌ 部分测试失败，请检查Web UI服务")
            return False

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Web UI测试脚本')
    parser.add_argument('--url', default='http://localhost:7860', help='Web UI地址')
    parser.add_argument('--wait', type=int, default=5, help='等待服务启动的时间（秒）')
    
    args = parser.parse_args()
    
    print(f"Web UI测试脚本")
    print(f"目标地址: {args.url}")
    
    if args.wait > 0:
        print(f"等待 {args.wait} 秒让服务启动...")
        time.sleep(args.wait)
    
    tester = WebUITester(args.url)
    success = tester.run_all_tests()
    
    sys.exit(0 if success else 1)

if __name__ == '__main__':
    main()
