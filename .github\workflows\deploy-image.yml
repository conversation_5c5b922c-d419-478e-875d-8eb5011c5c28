# This workflow uses actions that are not certified by GitHub.
# They are provided by a third-party and are governed by
# separate terms of service, privacy policy, and support
# documentation.

# GitHub recommends pinning actions to a commit SHA.
# To get a newer version, you will need to update the SHA.
# You can also reference a tag or branch, but the action may change without warning.

name: Create and publish a Docker image

on:
  push:
    branches: ['master']
  workflow_dispatch:
  create:
env:
  DOCKERHUB_IMAGE_NAME: hanfangyuan/dify-on-wechat
  GITHUB_REGISTRY: ghcr.io
  GITHUB_IMAGE_NAME: hanfangyuan4396/dify-on-wechat
  ACR_REGISTRY: registry.cn-hangzhou.aliyuncs.com
  ACR_IMAGE_NAME: ${{ secrets.ACR_NAMESPACE }}/dify-on-wechat

jobs:
  build-and-push-image:
    if: github.repository == 'hanfangyuan4396/dify-on-wechat'
    runs-on: ubuntu-latest
    permissions:
      contents: read
      packages: write

    steps:
      - name: Checkout repository
        uses: actions/checkout@v3

      - name: Login to Docker Hub
        uses: docker/login-action@v2
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}

      - name: Log in to the Container registry
        uses: docker/login-action@v2
        with:
          registry: ${{ env.GITHUB_REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}
      
      - name: Login to the ACR
        uses: docker/login-action@v2
        with:
          registry: ${{ env.ACR_REGISTRY }}
          username: ${{ secrets.ACR_USERNAME }}
          password: ${{ secrets.ACR_PASSWORD }}

      - name: Extract metadata (tags, labels) for Docker
        id: meta
        uses: docker/metadata-action@v4
        with:
          images: |
            ${{ env.ACR_REGISTRY }}/${{ env.ACR_IMAGE_NAME }}
            ${{ env.DOCKERHUB_IMAGE_NAME }}
            ${{ env.GITHUB_REGISTRY }}/${{ env.GITHUB_IMAGE_NAME }}

      - name: Build and push Docker image
        uses: docker/build-push-action@v3
        with:
          context: .
          push: true
          file: ./docker/Dockerfile.latest
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}

      - uses: actions/delete-package-versions@v4
        with:
          package-name: 'dify-on-wechat'
          package-type: 'container'
          min-versions-to-keep: 10
          delete-only-untagged-versions: 'true'
          token: ${{ secrets.GITHUB_TOKEN }}
