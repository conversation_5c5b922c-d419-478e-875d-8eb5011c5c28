<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>企业微信机器人管理平台</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- Socket.IO -->
    <script src="https://cdn.socket.io/4.5.0/socket.io.min.js"></script>
    <!-- 自定义样式 -->
    <link href="{{ url_for('static', filename='css/style.css') }}" rel="stylesheet">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="fas fa-robot me-2"></i>
                企业微信机器人管理平台
            </a>
            <div class="navbar-nav ms-auto">
                <span class="navbar-text me-3">
                    连接状态: <span id="connectionStatus" class="status-indicator status-stopped">连接中...</span>
                </span>
            </div>
        </div>
    </nav>

    <!-- 主容器 -->
    <div class="container main-container">
        <!-- 控制面板 -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-control me-2"></i>
                            控制面板
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col-md-6">
                                <div class="d-flex align-items-center mb-3">
                                    <span class="me-3">当前状态:</span>
                                    <span id="botStatus" class="status-indicator status-stopped">已停止</span>
                                </div>
                                <div class="d-flex align-items-center">
                                    <span class="me-3">运行时长:</span>
                                    <span id="uptime" class="fw-bold">0秒</span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="btn-group" role="group">
                                    <button id="startBtn" type="button" class="btn btn-success">
                                        <i class="fas fa-play me-1"></i>启动
                                    </button>
                                    <button id="stopBtn" type="button" class="btn btn-danger">
                                        <i class="fas fa-stop me-1"></i>停止
                                    </button>
                                    <button id="pauseBtn" type="button" class="btn btn-warning">
                                        <i class="fas fa-pause me-1"></i>暂停
                                    </button>
                                    <button id="resumeBtn" type="button" class="btn btn-info">
                                        <i class="fas fa-play me-1"></i>恢复
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 统计信息面板 -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card stats-card">
                    <div class="card-body text-center">
                        <div class="stats-number" id="messageReceived">0</div>
                        <div class="stats-label">接收消息</div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stats-card">
                    <div class="card-body text-center">
                        <div class="stats-number" id="messageReplied">0</div>
                        <div class="stats-label">回复消息</div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stats-card">
                    <div class="card-body text-center">
                        <div class="stats-number" id="successRate">0%</div>
                        <div class="stats-label">成功率</div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stats-card">
                    <div class="card-body text-center">
                        <div class="stats-number text-danger" id="errorCount">0</div>
                        <div class="stats-label">错误数</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 主要内容区域 -->
        <div class="row">
            <!-- 左侧列 -->
            <div class="col-lg-8">
                <!-- 实时日志面板 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">
                                <i class="fas fa-file-alt me-2"></i>
                                实时日志
                            </h5>
                            <div class="btn-group btn-group-sm">
                                <select id="logLevelFilter" class="form-select form-select-sm me-2">
                                    <option value="ALL">所有级别</option>
                                    <option value="DEBUG">DEBUG</option>
                                    <option value="INFO">INFO</option>
                                    <option value="WARNING">WARNING</option>
                                    <option value="ERROR">ERROR</option>
                                </select>
                                <input id="logSearch" type="text" class="form-control form-control-sm me-2" placeholder="搜索日志...">
                                <button id="clearLogsBtn" type="button" class="btn btn-outline-danger btn-sm">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="card-body p-0">
                        <div id="logContainer" class="log-container">
                            <!-- 日志内容将在这里动态加载 -->
                        </div>
                    </div>
                </div>

                <!-- 消息监控面板 -->
                <div class="card">
                    <div class="card-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">
                                <i class="fas fa-comments me-2"></i>
                                消息监控
                            </h5>
                            <div class="btn-group btn-group-sm">
                                <select id="messageTypeFilter" class="form-select form-select-sm me-2">
                                    <option value="ALL">所有类型</option>
                                    <option value="received">接收消息</option>
                                    <option value="sent">发送消息</option>
                                    <option value="error">错误消息</option>
                                </select>
                                <button id="clearMessagesBtn" type="button" class="btn btn-outline-danger btn-sm">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="card-body p-0">
                        <div id="messageContainer" class="message-container">
                            <!-- 消息内容将在这里动态加载 -->
                        </div>
                    </div>
                </div>
            </div>

            <!-- 右侧列 -->
            <div class="col-lg-4">
                <!-- 配置管理面板 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-cog me-2"></i>
                            配置管理
                        </h5>
                    </div>
                    <div class="card-body">
                        <form class="config-form">
                            <!-- AI配置 -->
                            <div class="config-section">
                                <h6>AI回复设置</h6>
                                <div class="form-check form-switch mb-3">
                                    <input class="form-check-input" type="checkbox" id="useDeepSeek">
                                    <label class="form-check-label" for="useDeepSeek">
                                        启用DeepSeek AI回复
                                    </label>
                                </div>
                                
                                <div id="aiConfigSection" style="display: none;">
                                    <div class="mb-3">
                                        <label for="deepseekApiKey" class="form-label">API Key</label>
                                        <input type="password" class="form-control" id="deepseekApiKey" placeholder="sk-...">
                                    </div>
                                    <div class="mb-3">
                                        <label for="deepseekApiBase" class="form-label">API Base URL</label>
                                        <input type="url" class="form-control" id="deepseekApiBase" value="https://api.deepseek.com/v1">
                                    </div>
                                    <button id="testApiBtn" type="button" class="btn btn-outline-primary btn-sm">
                                        <i class="fas fa-vial me-1"></i>测试API
                                    </button>
                                </div>
                            </div>

                            <!-- 回复配置 -->
                            <div class="config-section">
                                <h6>自动回复设置</h6>
                                <div class="mb-3">
                                    <label for="autoReplyText" class="form-label">自定义回复内容</label>
                                    <textarea class="form-control" id="autoReplyText" rows="3" placeholder="请输入自动回复内容..."></textarea>
                                </div>
                            </div>

                            <!-- 消息控制 -->
                            <div class="config-section">
                                <h6>消息控制</h6>
                                <div class="form-check form-switch mb-2">
                                    <input class="form-check-input" type="checkbox" id="enableGroupChat" checked>
                                    <label class="form-check-label" for="enableGroupChat">
                                        启用群聊消息
                                    </label>
                                </div>
                                <div class="form-check form-switch mb-3">
                                    <input class="form-check-input" type="checkbox" id="enableSingleChat" checked>
                                    <label class="form-check-label" for="enableSingleChat">
                                        启用单聊消息
                                    </label>
                                </div>
                            </div>

                            <!-- 操作按钮 -->
                            <div class="d-grid gap-2">
                                <button id="saveConfigBtn" type="button" class="btn btn-primary">
                                    <i class="fas fa-save me-1"></i>保存配置
                                </button>
                                <button id="resetConfigBtn" type="button" class="btn btn-outline-secondary">
                                    <i class="fas fa-undo me-1"></i>重置配置
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- 统计图表 -->
                <div class="card">
                    <div class="card-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">
                                <i class="fas fa-chart-line me-2"></i>
                                统计图表
                            </h5>
                            <button id="resetStatsBtn" type="button" class="btn btn-outline-warning btn-sm">
                                <i class="fas fa-refresh me-1"></i>重置
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <canvas id="statisticsChart" width="400" height="200"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- 自定义JavaScript -->
    <script src="{{ url_for('static', filename='js/main.js') }}"></script>
    <script src="{{ url_for('static', filename='js/charts.js') }}"></script>
</body>
</html>
