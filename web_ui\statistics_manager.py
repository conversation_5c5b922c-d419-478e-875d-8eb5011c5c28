# 统计管理器
import os
import sys
import time
import threading
from datetime import datetime, timedelta
from typing import Dict, Any, List
from collections import defaultdict, deque

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from common.log import logger

class MetricsCollector:
    """指标收集器"""
    
    def __init__(self, max_history_size: int = 1000):
        self.max_history_size = max_history_size
        self.message_history = deque(maxlen=max_history_size)
        self.reply_history = deque(maxlen=max_history_size)
        self.error_history = deque(maxlen=max_history_size)
        self.lock = threading.Lock()
    
    def record_message_received(self, message_info: Dict[str, Any]):
        """记录接收到的消息"""
        with self.lock:
            record = {
                'timestamp': datetime.now(),
                'type': 'received',
                'info': message_info
            }
            self.message_history.append(record)
    
    def record_message_replied(self, reply_info: Dict[str, Any]):
        """记录回复的消息"""
        with self.lock:
            record = {
                'timestamp': datetime.now(),
                'type': 'replied',
                'info': reply_info
            }
            self.reply_history.append(record)
    
    def record_error(self, error_info: Dict[str, Any]):
        """记录错误"""
        with self.lock:
            record = {
                'timestamp': datetime.now(),
                'type': 'error',
                'info': error_info
            }
            self.error_history.append(record)
    
    def get_statistics_by_time_range(self, hours: int = 24) -> Dict[str, Any]:
        """获取指定时间范围内的统计数据"""
        with self.lock:
            cutoff_time = datetime.now() - timedelta(hours=hours)
            
            # 过滤时间范围内的记录
            recent_messages = [r for r in self.message_history if r['timestamp'] >= cutoff_time]
            recent_replies = [r for r in self.reply_history if r['timestamp'] >= cutoff_time]
            recent_errors = [r for r in self.error_history if r['timestamp'] >= cutoff_time]
            
            return {
                'messages_received': len(recent_messages),
                'messages_replied': len(recent_replies),
                'errors': len(recent_errors),
                'time_range_hours': hours
            }
    
    def get_hourly_statistics(self, hours: int = 24) -> List[Dict[str, Any]]:
        """获取按小时统计的数据"""
        with self.lock:
            hourly_stats = []
            now = datetime.now()
            
            for i in range(hours):
                hour_start = now - timedelta(hours=i+1)
                hour_end = now - timedelta(hours=i)
                
                messages_count = len([r for r in self.message_history 
                                    if hour_start <= r['timestamp'] < hour_end])
                replies_count = len([r for r in self.reply_history 
                                   if hour_start <= r['timestamp'] < hour_end])
                errors_count = len([r for r in self.error_history 
                                  if hour_start <= r['timestamp'] < hour_end])
                
                hourly_stats.append({
                    'hour': hour_start.strftime('%H:00'),
                    'messages_received': messages_count,
                    'messages_replied': replies_count,
                    'errors': errors_count
                })
            
            return list(reversed(hourly_stats))
    
    def clear_history(self):
        """清空历史记录"""
        with self.lock:
            self.message_history.clear()
            self.reply_history.clear()
            self.error_history.clear()

class StatisticsManager:
    """统计管理器，收集和计算统计数据"""
    
    def __init__(self):
        self.start_time = datetime.now()
        self.reset_time = datetime.now()
        
        # 基础计数器
        self.message_received = 0
        self.message_replied = 0
        self.error_count = 0
        
        # 分类统计
        self.group_message_count = 0
        self.single_message_count = 0
        self.ai_reply_count = 0
        self.auto_reply_count = 0
        
        # 响应时间统计
        self.response_times = deque(maxlen=1000)
        self.total_response_time = 0.0
        
        # 指标收集器
        self.metrics_collector = MetricsCollector()
        
        # 线程锁
        self.lock = threading.Lock()
        
        logger.info("统计管理器初始化完成")
    
    def increment_received(self, message_type: str = "unknown", message_info: Dict[str, Any] = None):
        """增加接收消息计数"""
        with self.lock:
            self.message_received += 1
            
            if message_type == "group":
                self.group_message_count += 1
            elif message_type == "single":
                self.single_message_count += 1
            
            # 记录到指标收集器
            if message_info:
                self.metrics_collector.record_message_received(message_info)
    
    def increment_replied(self, reply_type: str = "unknown", response_time: float = 0.0, reply_info: Dict[str, Any] = None):
        """增加回复消息计数"""
        with self.lock:
            self.message_replied += 1
            
            if reply_type == "ai":
                self.ai_reply_count += 1
            elif reply_type == "auto":
                self.auto_reply_count += 1
            
            # 记录响应时间
            if response_time > 0:
                self.response_times.append(response_time)
                self.total_response_time += response_time
            
            # 记录到指标收集器
            if reply_info:
                self.metrics_collector.record_message_replied(reply_info)
    
    def increment_error(self, error_type: str = "unknown", error_info: Dict[str, Any] = None):
        """增加错误计数"""
        with self.lock:
            self.error_count += 1
            
            # 记录到指标收集器
            if error_info:
                self.metrics_collector.record_error(error_info)
    
    def get_success_rate(self) -> float:
        """计算成功率"""
        if self.message_received == 0:
            return 0.0
        return round((self.message_replied / self.message_received) * 100, 2)
    
    def get_average_response_time(self) -> float:
        """计算平均响应时间"""
        if len(self.response_times) == 0:
            return 0.0
        return round(sum(self.response_times) / len(self.response_times), 2)
    
    def get_uptime(self) -> int:
        """获取运行时长（秒）"""
        return int((datetime.now() - self.start_time).total_seconds())
    
    def get_uptime_since_reset(self) -> int:
        """获取重置后的运行时长（秒）"""
        return int((datetime.now() - self.reset_time).total_seconds())
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取完整统计数据"""
        with self.lock:
            uptime = self.get_uptime()
            uptime_since_reset = self.get_uptime_since_reset()
            
            # 计算每小时消息量
            messages_per_hour = 0
            if uptime_since_reset > 0:
                messages_per_hour = round((self.message_received / uptime_since_reset) * 3600, 2)
            
            return {
                # 基础统计
                'message_received': self.message_received,
                'message_replied': self.message_replied,
                'error_count': self.error_count,
                'success_rate': self.get_success_rate(),
                
                # 分类统计
                'group_message_count': self.group_message_count,
                'single_message_count': self.single_message_count,
                'ai_reply_count': self.ai_reply_count,
                'auto_reply_count': self.auto_reply_count,
                
                # 时间统计
                'uptime': uptime,
                'uptime_since_reset': uptime_since_reset,
                'start_time': self.start_time.isoformat(),
                'reset_time': self.reset_time.isoformat(),
                
                # 性能统计
                'average_response_time': self.get_average_response_time(),
                'messages_per_hour': messages_per_hour,
                
                # 格式化显示
                'uptime_formatted': self._format_duration(uptime),
                'uptime_since_reset_formatted': self._format_duration(uptime_since_reset)
            }
    
    def get_detailed_statistics(self) -> Dict[str, Any]:
        """获取详细统计数据，包括历史数据"""
        basic_stats = self.get_statistics()
        
        # 添加历史统计数据
        hourly_stats = self.metrics_collector.get_hourly_statistics(24)
        recent_stats = self.metrics_collector.get_statistics_by_time_range(24)
        
        basic_stats.update({
            'hourly_statistics': hourly_stats,
            'last_24h_statistics': recent_stats
        })
        
        return basic_stats
    
    def reset_statistics(self):
        """重置统计数据"""
        with self.lock:
            self.reset_time = datetime.now()
            self.message_received = 0
            self.message_replied = 0
            self.error_count = 0
            self.group_message_count = 0
            self.single_message_count = 0
            self.ai_reply_count = 0
            self.auto_reply_count = 0
            self.response_times.clear()
            self.total_response_time = 0.0
            
            # 清空指标收集器历史
            self.metrics_collector.clear_history()
            
            logger.info("统计数据已重置")
    
    def _format_duration(self, seconds: int) -> str:
        """格式化时长显示"""
        if seconds < 60:
            return f"{seconds}秒"
        elif seconds < 3600:
            minutes = seconds // 60
            remaining_seconds = seconds % 60
            return f"{minutes}分{remaining_seconds}秒"
        else:
            hours = seconds // 3600
            remaining_minutes = (seconds % 3600) // 60
            remaining_seconds = seconds % 60
            return f"{hours}小时{remaining_minutes}分{remaining_seconds}秒"
    
    def export_statistics(self) -> Dict[str, Any]:
        """导出统计数据"""
        return {
            'export_time': datetime.now().isoformat(),
            'statistics': self.get_detailed_statistics()
        }
    
    def get_performance_metrics(self) -> Dict[str, Any]:
        """获取性能指标"""
        with self.lock:
            return {
                'total_response_times': len(self.response_times),
                'average_response_time': self.get_average_response_time(),
                'min_response_time': min(self.response_times) if self.response_times else 0,
                'max_response_time': max(self.response_times) if self.response_times else 0,
                'messages_per_hour': round((self.message_received / max(self.get_uptime_since_reset(), 1)) * 3600, 2)
            }
