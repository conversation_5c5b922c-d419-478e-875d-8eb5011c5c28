# encoding:utf-8

from bot.bot import <PERSON><PERSON>
from bridge.context import ContextType
from bridge.reply import Reply, ReplyType
from common.log import logger
from config import conf


class MockBot(Bot):
    def __init__(self):
        super().__init__()
        self.auto_reply_text = conf().get("auto_reply_text", "你好啊")

    def reply(self, query, context=None):
        """
        简单的固定回复功能
        """
        try:
            if context and context.type == ContextType.TEXT:
                logger.info(f"[MockBot] 收到消息: {query}")
                
                # 返回固定的回复文本
                reply = Reply(ReplyType.TEXT, self.auto_reply_text)
                logger.info(f"[MockBot] 回复消息: {self.auto_reply_text}")
                return reply
            else:
                reply = Reply(ReplyType.ERROR, "不支持的消息类型")
                return reply
                
        except Exception as e:
            logger.error(f"[MockBot] 回复失败: {e}")
            reply = Reply(ReplyType.ERROR, "回复失败")
            return reply

    def reply_text(self, session, query, context=None):
        """
        文本消息回复
        """
        return self.reply(query, context)
