# WebSocket管理器
import os
import sys
import threading
from typing import Set, Dict, Any
from flask_socketio import Socket<PERSON>, emit

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from common.log import logger

class WebSocketManager:
    """WebSocket管理器，处理实时通信"""
    
    def __init__(self, socketio: SocketIO):
        self.socketio = socketio
        self.connections: Set[str] = set()
        self.lock = threading.Lock()
        
        logger.info("WebSocket管理器初始化完成")
    
    def add_connection(self, session_id: str):
        """添加连接"""
        with self.lock:
            self.connections.add(session_id)
            logger.debug(f"WebSocket连接已添加: {session_id}, 当前连接数: {len(self.connections)}")
    
    def remove_connection(self, session_id: str):
        """移除连接"""
        with self.lock:
            self.connections.discard(session_id)
            logger.debug(f"WebSocket连接已移除: {session_id}, 当前连接数: {len(self.connections)}")
    
    def get_connection_count(self) -> int:
        """获取连接数"""
        with self.lock:
            return len(self.connections)
    
    def broadcast_message(self, event: str, data: Dict[str, Any]):
        """广播消息到所有连接的客户端"""
        try:
            self.socketio.emit(event, data)
            logger.debug(f"广播消息: {event}")
        except Exception as e:
            logger.error(f"广播消息失败: {e}")
    
    def send_to_client(self, session_id: str, event: str, data: Dict[str, Any]):
        """发送消息到特定客户端"""
        try:
            self.socketio.emit(event, data, room=session_id)
            logger.debug(f"发送消息到客户端 {session_id}: {event}")
        except Exception as e:
            logger.error(f"发送消息到客户端失败: {e}")
    
    # 状态更新相关方法
    def broadcast_status_update(self, status_data: Dict[str, Any] = None):
        """广播状态更新"""
        if status_data is None:
            # 如果没有提供状态数据，从应用上下文获取
            try:
                from flask import current_app
                if hasattr(current_app, 'bot_controller'):
                    status_data = current_app.bot_controller.get_status()
                else:
                    status_data = {'status': 'unknown'}
            except:
                status_data = {'status': 'unknown'}
        
        self.broadcast_message('status_update', status_data)
    
    def broadcast_statistics_update(self, stats_data: Dict[str, Any] = None):
        """广播统计数据更新"""
        if stats_data is None:
            try:
                from flask import current_app
                if hasattr(current_app, 'statistics_manager'):
                    stats_data = current_app.statistics_manager.get_statistics()
                else:
                    stats_data = {}
            except:
                stats_data = {}
        
        self.broadcast_message('statistics_update', stats_data)
    
    def broadcast_config_changed(self, config_data: Dict[str, Any] = None):
        """广播配置变更"""
        if config_data is None:
            try:
                from flask import current_app
                if hasattr(current_app, 'config_manager'):
                    config_data = current_app.config_manager.get_web_ui_config()
                else:
                    config_data = {}
            except:
                config_data = {}
        
        self.broadcast_message('config_changed', config_data)
    
    # 日志相关方法
    def broadcast_log_message(self, log_data: Dict[str, Any]):
        """广播日志消息"""
        self.broadcast_message('log_message', log_data)
    
    def send_logs_data(self, session_id: str, logs_data: list):
        """发送日志数据到特定客户端"""
        self.send_to_client(session_id, 'logs_data', logs_data)
    
    # 消息监控相关方法
    def broadcast_message_received(self, message_data: Dict[str, Any]):
        """广播接收到的消息"""
        self.broadcast_message('message_received', message_data)
    
    def broadcast_message_sent(self, message_data: Dict[str, Any]):
        """广播发送的消息"""
        self.broadcast_message('message_sent', message_data)
    
    def broadcast_message_error(self, error_data: Dict[str, Any]):
        """广播消息错误"""
        self.broadcast_message('message_error', error_data)
    
    def send_messages_data(self, session_id: str, messages_data: list):
        """发送消息数据到特定客户端"""
        self.send_to_client(session_id, 'messages_data', messages_data)
    
    # 通知相关方法
    def broadcast_notification(self, notification_type: str, message: str, level: str = "info"):
        """广播通知消息"""
        notification_data = {
            'type': notification_type,
            'message': message,
            'level': level,
            'timestamp': self._get_current_timestamp()
        }
        self.broadcast_message('notification', notification_data)
    
    def broadcast_error(self, error_message: str, error_code: str = None):
        """广播错误消息"""
        error_data = {
            'message': error_message,
            'code': error_code,
            'timestamp': self._get_current_timestamp()
        }
        self.broadcast_message('error', error_data)
    
    def broadcast_success(self, success_message: str):
        """广播成功消息"""
        success_data = {
            'message': success_message,
            'timestamp': self._get_current_timestamp()
        }
        self.broadcast_message('success', success_data)
    
    # 系统监控相关方法
    def broadcast_system_info(self, system_data: Dict[str, Any]):
        """广播系统信息"""
        self.broadcast_message('system_info', system_data)
    
    def broadcast_performance_metrics(self, metrics_data: Dict[str, Any]):
        """广播性能指标"""
        self.broadcast_message('performance_metrics', metrics_data)
    
    # 实用方法
    def _get_current_timestamp(self) -> str:
        """获取当前时间戳"""
        from datetime import datetime
        return datetime.now().isoformat()
    
    def ping_clients(self):
        """向所有客户端发送ping"""
        ping_data = {
            'timestamp': self._get_current_timestamp(),
            'connection_count': self.get_connection_count()
        }
        self.broadcast_message('ping', ping_data)
    
    def broadcast_heartbeat(self):
        """广播心跳"""
        heartbeat_data = {
            'timestamp': self._get_current_timestamp(),
            'server_status': 'running'
        }
        self.broadcast_message('heartbeat', heartbeat_data)
    
    # 批量操作方法
    def broadcast_bulk_update(self, updates: Dict[str, Any]):
        """批量广播更新"""
        bulk_data = {
            'updates': updates,
            'timestamp': self._get_current_timestamp()
        }
        self.broadcast_message('bulk_update', bulk_data)
    
    def send_initial_data(self, session_id: str):
        """向新连接的客户端发送初始数据"""
        try:
            from flask import current_app
            
            # 发送当前状态
            if hasattr(current_app, 'bot_controller'):
                status_data = current_app.bot_controller.get_status()
                self.send_to_client(session_id, 'status_update', status_data)
            
            # 发送统计数据
            if hasattr(current_app, 'statistics_manager'):
                stats_data = current_app.statistics_manager.get_statistics()
                self.send_to_client(session_id, 'statistics_update', stats_data)
            
            # 发送配置数据
            if hasattr(current_app, 'config_manager'):
                config_data = current_app.config_manager.get_web_ui_config()
                self.send_to_client(session_id, 'config_changed', config_data)
            
            # 发送最近的日志
            if hasattr(current_app, 'log_manager'):
                recent_logs = current_app.log_manager.get_recent_logs(10)
                self.send_to_client(session_id, 'logs_data', recent_logs)
            
            # 发送最近的消息
            if hasattr(current_app, 'message_monitor'):
                recent_messages = current_app.message_monitor.get_recent_messages(10)
                self.send_to_client(session_id, 'messages_data', recent_messages)
            
            logger.debug(f"已向客户端 {session_id} 发送初始数据")
            
        except Exception as e:
            logger.error(f"发送初始数据失败: {e}")
    
    def get_connection_info(self) -> Dict[str, Any]:
        """获取连接信息"""
        with self.lock:
            return {
                'total_connections': len(self.connections),
                'active_connections': list(self.connections),
                'timestamp': self._get_current_timestamp()
            }
