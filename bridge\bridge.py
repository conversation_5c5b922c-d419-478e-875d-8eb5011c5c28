from bot.bot_factory import create_bot
from bridge.context import Context
from bridge.reply import Reply
from common import const
from common.log import logger
from common.singleton import singleton
from config import conf


@singleton
class Bridge(object):
    def __init__(self):
        self.btype = {
            "chat": const.MOCK,
        }

        # 这边取配置的模型
        bot_type = conf().get("bot_type")
        if bot_type:
            self.btype["chat"] = bot_type
        else:
            # 检查是否启用DeepSeek
            use_deepseek = conf().get("use_deepseek", False)
            if use_deepseek:
                self.btype["chat"] = "deepseek"
            else:
                model_type = conf().get("model") or const.MOCK
                if model_type in [const.MOCK]:
                    self.btype["chat"] = const.MOCK
                else:
                    # 默认使用mock模型
                    self.btype["chat"] = const.MOCK

        self.bots = {}
        self.chat_bots = {}

    # 模型对应的接口
    def get_bot(self, typename):
        if self.bots.get(typename) is None:
            logger.info("create bot {} for {}".format(self.btype[typename], typename))
            if typename == "chat":
                self.bots[typename] = create_bot(self.btype[typename])
        return self.bots[typename]

    def get_bot_type(self, typename):
        return self.btype[typename]

    def fetch_reply_content(self, query, context: Context) -> Reply:
        return self.get_bot("chat").reply(query, context)



    def find_chat_bot(self, bot_type: str):
        if self.chat_bots.get(bot_type) is None:
            self.chat_bots[bot_type] = create_bot(bot_type)
        return self.chat_bots.get(bot_type)

    def reset_bot(self):
        """
        重置bot路由
        """
        self.bots = {}

    def switch_bot_type(self, bot_type: str):
        """
        动态切换机器人类型
        """
        try:
            self.btype["chat"] = bot_type
            # 清除现有的bot实例，强制重新创建
            if "chat" in self.bots:
                del self.bots["chat"]
            logger.info(f"机器人类型已切换为: {bot_type}")
            return True
        except Exception as e:
            logger.error(f"切换机器人类型失败: {e}")
            return False
