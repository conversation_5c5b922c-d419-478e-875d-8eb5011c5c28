# 日志管理器
import os
import sys
import logging
import threading
from datetime import datetime
from typing import List, Dict, Any, Optional
from collections import deque
import re

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from common.log import logger

class LogRecord:
    """日志记录类"""
    
    def __init__(self, level: str, message: str, timestamp: datetime = None, module: str = ""):
        self.level = level
        self.message = message
        self.timestamp = timestamp or datetime.now()
        self.module = module
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'level': self.level,
            'message': self.message,
            'timestamp': self.timestamp.isoformat(),
            'module': self.module,
            'formatted_time': self.timestamp.strftime('%Y-%m-%d %H:%M:%S')
        }

class WebUILogHandler(logging.Handler):
    """Web UI日志处理器"""
    
    def __init__(self, log_manager):
        super().__init__()
        self.log_manager = log_manager
        self.setFormatter(logging.Formatter(
            '%(asctime)s [%(levelname)s] [%(name)s] %(message)s'
        ))
    
    def emit(self, record):
        """处理日志记录"""
        try:
            # 格式化日志消息
            message = self.format(record)
            
            # 提取模块名
            module = getattr(record, 'name', 'unknown')
            
            # 创建日志记录
            log_record = LogRecord(
                level=record.levelname,
                message=record.getMessage(),
                timestamp=datetime.fromtimestamp(record.created),
                module=module
            )
            
            # 添加到日志管理器
            self.log_manager.add_log_record(log_record)
            
        except Exception:
            self.handleError(record)

class LogManager:
    """日志管理器，处理日志收集、过滤和搜索"""
    
    def __init__(self, max_logs: int = 1000):
        self.max_logs = max_logs
        self.logs = deque(maxlen=max_logs)
        self.lock = threading.Lock()
        self.websocket_manager = None
        
        # 日志级别映射
        self.level_priority = {
            'DEBUG': 10,
            'INFO': 20,
            'WARNING': 30,
            'ERROR': 40,
            'CRITICAL': 50
        }
        
        # 创建并注册自定义日志处理器
        self.log_handler = WebUILogHandler(self)
        self.log_handler.setLevel(logging.DEBUG)
        
        # 添加到根日志记录器
        root_logger = logging.getLogger()
        root_logger.addHandler(self.log_handler)
        
        logger.info("日志管理器初始化完成")
    
    def set_websocket_manager(self, websocket_manager):
        """设置WebSocket管理器"""
        self.websocket_manager = websocket_manager
    
    def add_log_record(self, log_record: LogRecord):
        """添加日志记录"""
        with self.lock:
            self.logs.append(log_record)
            
            # 实时推送日志到WebSocket客户端
            if self.websocket_manager:
                self.websocket_manager.broadcast_log_message(log_record.to_dict())
    
    def get_logs(self, level: str = "ALL", limit: int = 100, search: str = "") -> List[Dict[str, Any]]:
        """获取日志记录"""
        with self.lock:
            filtered_logs = list(self.logs)
            
            # 按级别过滤
            if level != "ALL" and level in self.level_priority:
                min_priority = self.level_priority[level]
                filtered_logs = [
                    log for log in filtered_logs 
                    if self.level_priority.get(log.level, 0) >= min_priority
                ]
            
            # 按搜索关键词过滤
            if search:
                search_lower = search.lower()
                filtered_logs = [
                    log for log in filtered_logs
                    if search_lower in log.message.lower() or search_lower in log.module.lower()
                ]
            
            # 限制数量并转换为字典
            limited_logs = list(filtered_logs)[-limit:] if limit > 0 else filtered_logs
            return [log.to_dict() for log in limited_logs]
    
    def get_logs_by_level(self, level: str, limit: int = 100) -> List[Dict[str, Any]]:
        """按级别获取日志"""
        return self.get_logs(level=level, limit=limit)
    
    def search_logs(self, keyword: str, limit: int = 100) -> List[Dict[str, Any]]:
        """搜索日志"""
        return self.get_logs(search=keyword, limit=limit)
    
    def get_recent_logs(self, minutes: int = 10) -> List[Dict[str, Any]]:
        """获取最近几分钟的日志"""
        with self.lock:
            cutoff_time = datetime.now().timestamp() - (minutes * 60)
            recent_logs = [
                log for log in self.logs
                if log.timestamp.timestamp() >= cutoff_time
            ]
            return [log.to_dict() for log in recent_logs]
    
    def get_error_logs(self, limit: int = 50) -> List[Dict[str, Any]]:
        """获取错误日志"""
        return self.get_logs_by_level("ERROR", limit)
    
    def get_warning_logs(self, limit: int = 50) -> List[Dict[str, Any]]:
        """获取警告日志"""
        return self.get_logs_by_level("WARNING", limit)
    
    def clear_logs(self):
        """清空日志"""
        with self.lock:
            self.logs.clear()
            logger.info("日志已清空")
    
    def get_log_statistics(self) -> Dict[str, Any]:
        """获取日志统计信息"""
        with self.lock:
            stats = {
                'total_logs': len(self.logs),
                'debug_count': 0,
                'info_count': 0,
                'warning_count': 0,
                'error_count': 0,
                'critical_count': 0
            }
            
            for log in self.logs:
                level_key = f"{log.level.lower()}_count"
                if level_key in stats:
                    stats[level_key] += 1
            
            return stats
    
    def export_logs(self, level: str = "ALL", format: str = "json") -> str:
        """导出日志"""
        logs = self.get_logs(level=level, limit=0)  # 获取所有日志
        
        if format == "json":
            import json
            return json.dumps(logs, ensure_ascii=False, indent=2)
        elif format == "txt":
            lines = []
            for log in logs:
                line = f"[{log['formatted_time']}] [{log['level']}] [{log['module']}] {log['message']}"
                lines.append(line)
            return "\n".join(lines)
        else:
            raise ValueError(f"不支持的导出格式: {format}")
    
    def get_log_levels(self) -> List[str]:
        """获取可用的日志级别"""
        return ["ALL", "DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
    
    def set_log_level(self, level: str):
        """设置日志级别"""
        if level in self.level_priority:
            self.log_handler.setLevel(getattr(logging, level))
            logger.info(f"日志级别已设置为: {level}")
        else:
            logger.warning(f"无效的日志级别: {level}")
    
    def get_logs_by_module(self, module: str, limit: int = 100) -> List[Dict[str, Any]]:
        """按模块获取日志"""
        with self.lock:
            module_logs = [
                log for log in self.logs
                if module.lower() in log.module.lower()
            ]
            limited_logs = module_logs[-limit:] if limit > 0 else module_logs
            return [log.to_dict() for log in limited_logs]
    
    def get_unique_modules(self) -> List[str]:
        """获取所有唯一的模块名"""
        with self.lock:
            modules = set()
            for log in self.logs:
                if log.module:
                    modules.add(log.module)
            return sorted(list(modules))
    
    def filter_logs_advanced(self, 
                           level: str = "ALL",
                           module: str = "",
                           search: str = "",
                           start_time: Optional[datetime] = None,
                           end_time: Optional[datetime] = None,
                           limit: int = 100) -> List[Dict[str, Any]]:
        """高级日志过滤"""
        with self.lock:
            filtered_logs = list(self.logs)
            
            # 按级别过滤
            if level != "ALL" and level in self.level_priority:
                min_priority = self.level_priority[level]
                filtered_logs = [
                    log for log in filtered_logs 
                    if self.level_priority.get(log.level, 0) >= min_priority
                ]
            
            # 按模块过滤
            if module:
                module_lower = module.lower()
                filtered_logs = [
                    log for log in filtered_logs
                    if module_lower in log.module.lower()
                ]
            
            # 按搜索关键词过滤
            if search:
                search_lower = search.lower()
                filtered_logs = [
                    log for log in filtered_logs
                    if search_lower in log.message.lower()
                ]
            
            # 按时间范围过滤
            if start_time:
                filtered_logs = [
                    log for log in filtered_logs
                    if log.timestamp >= start_time
                ]
            
            if end_time:
                filtered_logs = [
                    log for log in filtered_logs
                    if log.timestamp <= end_time
                ]
            
            # 限制数量并转换为字典
            limited_logs = filtered_logs[-limit:] if limit > 0 else filtered_logs
            return [log.to_dict() for log in limited_logs]
