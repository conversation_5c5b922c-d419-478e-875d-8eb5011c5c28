// 企业微信机器人Web UI主要JavaScript文件

class BotController {
    constructor() {
        this.socket = null;
        this.isConnected = false;
        this.currentStatus = 'stopped';
        this.statistics = {};
        this.config = {};
        
        this.init();
    }
    
    init() {
        this.initializeWebSocket();
        this.bindEvents();
        this.loadInitialData();
        this.startPeriodicUpdates();
    }
    
    // WebSocket连接管理
    initializeWebSocket() {
        this.socket = io();
        
        this.socket.on('connect', () => {
            console.log('WebSocket连接成功');
            this.isConnected = true;
            this.updateConnectionStatus(true);
        });
        
        this.socket.on('disconnect', () => {
            console.log('WebSocket连接断开');
            this.isConnected = false;
            this.updateConnectionStatus(false);
        });
        
        // 监听各种事件
        this.socket.on('status_update', (data) => {
            this.updateStatus(data);
        });
        
        this.socket.on('statistics_update', (data) => {
            this.updateStatistics(data);
        });
        
        this.socket.on('log_message', (data) => {
            this.addLogMessage(data);
        });
        
        this.socket.on('message_received', (data) => {
            this.addMessage(data, 'received');
        });
        
        this.socket.on('message_sent', (data) => {
            this.addMessage(data, 'sent');
        });
        
        this.socket.on('config_changed', (data) => {
            this.updateConfig(data);
        });
        
        this.socket.on('notification', (data) => {
            this.showNotification(data.message, data.level);
        });
        
        this.socket.on('error', (data) => {
            this.showNotification(data.message, 'error');
        });
    }
    
    // 事件绑定
    bindEvents() {
        // 控制按钮事件
        document.getElementById('startBtn').addEventListener('click', () => this.startBot());
        document.getElementById('stopBtn').addEventListener('click', () => this.stopBot());
        document.getElementById('pauseBtn').addEventListener('click', () => this.pauseBot());
        document.getElementById('resumeBtn').addEventListener('click', () => this.resumeBot());
        
        // 配置相关事件
        document.getElementById('saveConfigBtn').addEventListener('click', () => this.saveConfig());
        document.getElementById('resetConfigBtn').addEventListener('click', () => this.resetConfig());
        document.getElementById('testApiBtn').addEventListener('click', () => this.testDeepSeekAPI());
        
        // 日志相关事件
        document.getElementById('clearLogsBtn').addEventListener('click', () => this.clearLogs());
        document.getElementById('logLevelFilter').addEventListener('change', (e) => this.filterLogs(e.target.value));
        document.getElementById('logSearch').addEventListener('input', (e) => this.searchLogs(e.target.value));
        
        // 消息相关事件
        document.getElementById('clearMessagesBtn').addEventListener('click', () => this.clearMessages());
        document.getElementById('messageTypeFilter').addEventListener('change', (e) => this.filterMessages(e.target.value));
        
        // 统计相关事件
        document.getElementById('resetStatsBtn').addEventListener('click', () => this.resetStatistics());
        
        // AI开关事件
        document.getElementById('useDeepSeek').addEventListener('change', (e) => this.toggleAI(e.target.checked));
    }
    
    // 加载初始数据
    async loadInitialData() {
        try {
            // 加载状态
            const statusResponse = await fetch('/api/status');
            if (statusResponse.ok) {
                const statusData = await statusResponse.json();
                this.updateStatus(statusData.data);
            }
            
            // 加载统计数据
            const statsResponse = await fetch('/api/statistics');
            if (statsResponse.ok) {
                const statsData = await statsResponse.json();
                this.updateStatistics(statsData.data);
            }
            
            // 加载配置
            const configResponse = await fetch('/api/config');
            if (configResponse.ok) {
                const configData = await configResponse.json();
                this.updateConfig(configData.data);
            }
            
            // 加载日志
            this.loadLogs();
            
            // 加载消息
            this.loadMessages();
            
        } catch (error) {
            console.error('加载初始数据失败:', error);
            this.showNotification('加载初始数据失败', 'error');
        }
    }
    
    // 机器人控制方法
    async startBot() {
        try {
            this.setButtonLoading('startBtn', true);
            const response = await fetch('/api/start', { method: 'POST' });
            const data = await response.json();
            
            if (data.success) {
                this.showNotification(data.message, 'success');
            } else {
                this.showNotification(data.error, 'error');
            }
        } catch (error) {
            this.showNotification('启动失败', 'error');
        } finally {
            this.setButtonLoading('startBtn', false);
        }
    }
    
    async stopBot() {
        try {
            this.setButtonLoading('stopBtn', true);
            const response = await fetch('/api/stop', { method: 'POST' });
            const data = await response.json();
            
            if (data.success) {
                this.showNotification(data.message, 'success');
            } else {
                this.showNotification(data.error, 'error');
            }
        } catch (error) {
            this.showNotification('停止失败', 'error');
        } finally {
            this.setButtonLoading('stopBtn', false);
        }
    }
    
    async pauseBot() {
        try {
            this.setButtonLoading('pauseBtn', true);
            const response = await fetch('/api/pause', { method: 'POST' });
            const data = await response.json();
            
            if (data.success) {
                this.showNotification(data.message, 'success');
            } else {
                this.showNotification(data.error, 'error');
            }
        } catch (error) {
            this.showNotification('暂停失败', 'error');
        } finally {
            this.setButtonLoading('pauseBtn', false);
        }
    }
    
    async resumeBot() {
        try {
            this.setButtonLoading('resumeBtn', true);
            const response = await fetch('/api/resume', { method: 'POST' });
            const data = await response.json();
            
            if (data.success) {
                this.showNotification(data.message, 'success');
            } else {
                this.showNotification(data.error, 'error');
            }
        } catch (error) {
            this.showNotification('恢复失败', 'error');
        } finally {
            this.setButtonLoading('resumeBtn', false);
        }
    }
    
    // 状态更新
    updateStatus(statusData) {
        this.currentStatus = statusData.status;
        
        // 更新状态显示
        const statusElement = document.getElementById('botStatus');
        const statusText = this.getStatusText(statusData.status);
        const statusClass = `status-${statusData.status}`;
        
        statusElement.textContent = statusText;
        statusElement.className = `status-indicator ${statusClass}`;
        
        // 更新运行时长
        if (statusData.uptime) {
            document.getElementById('uptime').textContent = this.formatDuration(statusData.uptime);
        }
        
        // 更新按钮状态
        this.updateControlButtons(statusData.status);
    }
    
    // 统计数据更新
    updateStatistics(statsData) {
        this.statistics = statsData;
        
        // 更新统计显示
        document.getElementById('messageReceived').textContent = statsData.message_received || 0;
        document.getElementById('messageReplied').textContent = statsData.message_replied || 0;
        document.getElementById('successRate').textContent = (statsData.success_rate || 0) + '%';
        document.getElementById('errorCount').textContent = statsData.error_count || 0;
        
        // 更新图表（如果有的话）
        if (window.updateCharts) {
            window.updateCharts(statsData);
        }
    }
    
    // 配置更新
    updateConfig(configData) {
        this.config = configData;

        // 更新配置表单
        document.getElementById('useDeepSeek').checked = configData.use_deepseek || false;
        document.getElementById('deepseekApiKey').value = configData.deepseek_api_key || '';
        document.getElementById('deepseekApiBase').value = configData.deepseek_api_base || '';
        document.getElementById('autoReplyText').value = configData.auto_reply_text || '';
        document.getElementById('enableGroupChat').checked = configData.enable_group_chat !== false;
        document.getElementById('enableSingleChat').checked = configData.enable_single_chat !== false;

        // 更新AI相关UI状态
        this.updateAIConfigUI(configData.use_deepseek || false);
    }

    // 配置管理方法
    async saveConfig() {
        try {
            const configData = {
                use_deepseek: document.getElementById('useDeepSeek').checked,
                deepseek_api_key: document.getElementById('deepseekApiKey').value,
                deepseek_api_base: document.getElementById('deepseekApiBase').value,
                auto_reply_text: document.getElementById('autoReplyText').value,
                enable_group_chat: document.getElementById('enableGroupChat').checked,
                enable_single_chat: document.getElementById('enableSingleChat').checked
            };

            const response = await fetch('/api/config', {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(configData)
            });

            const data = await response.json();
            if (data.success) {
                this.showNotification('配置保存成功', 'success');
            } else {
                this.showNotification(data.error, 'error');
            }
        } catch (error) {
            this.showNotification('保存配置失败', 'error');
        }
    }

    async resetConfig() {
        if (confirm('确定要重置配置吗？这将恢复所有默认设置。')) {
            try {
                // 这里可以实现重置配置的逻辑
                this.showNotification('配置重置功能待实现', 'info');
            } catch (error) {
                this.showNotification('重置配置失败', 'error');
            }
        }
    }

    async testDeepSeekAPI() {
        try {
            this.setButtonLoading('testApiBtn', true);
            // 这里可以实现API测试逻辑
            this.showNotification('API测试功能待实现', 'info');
        } catch (error) {
            this.showNotification('API测试失败', 'error');
        } finally {
            this.setButtonLoading('testApiBtn', false);
        }
    }

    toggleAI(enabled) {
        this.updateAIConfigUI(enabled);
        // 可以在这里实现实时切换AI的逻辑
    }

    updateAIConfigUI(enabled) {
        const aiConfigSection = document.getElementById('aiConfigSection');
        if (aiConfigSection) {
            aiConfigSection.style.display = enabled ? 'block' : 'none';
        }
    }

    // 日志管理方法
    async loadLogs() {
        try {
            const response = await fetch('/api/logs?limit=100');
            if (response.ok) {
                const data = await response.json();
                this.displayLogs(data.data);
            }
        } catch (error) {
            console.error('加载日志失败:', error);
        }
    }

    displayLogs(logs) {
        const logContainer = document.getElementById('logContainer');
        logContainer.innerHTML = '';

        logs.forEach(log => {
            this.addLogMessage(log);
        });

        // 滚动到底部
        logContainer.scrollTop = logContainer.scrollHeight;
    }

    addLogMessage(logData) {
        const logContainer = document.getElementById('logContainer');
        const logEntry = document.createElement('div');
        logEntry.className = `log-entry ${logData.level.toLowerCase()}`;

        logEntry.innerHTML = `
            <span class="log-timestamp">${logData.formatted_time}</span>
            <span class="log-level ${logData.level.toLowerCase()}">${logData.level}</span>
            <span class="log-message">${logData.message}</span>
        `;

        logContainer.appendChild(logEntry);

        // 保持最新日志可见
        logContainer.scrollTop = logContainer.scrollHeight;

        // 限制日志条数
        while (logContainer.children.length > 1000) {
            logContainer.removeChild(logContainer.firstChild);
        }
    }

    async clearLogs() {
        if (confirm('确定要清空所有日志吗？')) {
            try {
                const response = await fetch('/api/logs', { method: 'DELETE' });
                const data = await response.json();

                if (data.success) {
                    document.getElementById('logContainer').innerHTML = '';
                    this.showNotification('日志已清空', 'success');
                } else {
                    this.showNotification(data.error, 'error');
                }
            } catch (error) {
                this.showNotification('清空日志失败', 'error');
            }
        }
    }

    filterLogs(level) {
        // 实现日志级别过滤
        const logEntries = document.querySelectorAll('.log-entry');
        logEntries.forEach(entry => {
            if (level === 'ALL' || entry.classList.contains(level.toLowerCase())) {
                entry.style.display = 'block';
            } else {
                entry.style.display = 'none';
            }
        });
    }

    searchLogs(keyword) {
        // 实现日志搜索
        const logEntries = document.querySelectorAll('.log-entry');
        const searchTerm = keyword.toLowerCase();

        logEntries.forEach(entry => {
            const text = entry.textContent.toLowerCase();
            if (!keyword || text.includes(searchTerm)) {
                entry.style.display = 'block';
            } else {
                entry.style.display = 'none';
            }
        });
    }

    // 消息管理方法
    async loadMessages() {
        try {
            const response = await fetch('/api/messages?limit=50');
            if (response.ok) {
                const data = await response.json();
                this.displayMessages(data.data);
            }
        } catch (error) {
            console.error('加载消息失败:', error);
        }
    }

    displayMessages(messages) {
        const messageContainer = document.getElementById('messageContainer');
        messageContainer.innerHTML = '';

        messages.forEach(message => {
            this.addMessage(message);
        });
    }

    addMessage(messageData, type = null) {
        const messageContainer = document.getElementById('messageContainer');
        const messageItem = document.createElement('div');
        const messageType = type || messageData.type;

        messageItem.className = `message-item ${messageType}`;
        messageItem.innerHTML = `
            <div class="message-header">
                <span class="message-type">${this.getMessageTypeText(messageType)}</span>
                <span class="message-time">${messageData.formatted_time || messageData.timestamp}</span>
            </div>
            <div class="message-content">${messageData.content}</div>
            ${messageData.sender ? `<div class="message-sender">发送者: ${messageData.sender}</div>` : ''}
        `;

        messageContainer.appendChild(messageItem);

        // 保持最新消息可见
        messageContainer.scrollTop = messageContainer.scrollHeight;

        // 限制消息条数
        while (messageContainer.children.length > 200) {
            messageContainer.removeChild(messageContainer.firstChild);
        }
    }

    getMessageTypeText(type) {
        const typeMap = {
            'received': '接收',
            'sent': '发送',
            'error': '错误'
        };
        return typeMap[type] || type;
    }

    async clearMessages() {
        if (confirm('确定要清空所有消息记录吗？')) {
            try {
                const response = await fetch('/api/messages', { method: 'DELETE' });
                const data = await response.json();

                if (data.success) {
                    document.getElementById('messageContainer').innerHTML = '';
                    this.showNotification('消息记录已清空', 'success');
                } else {
                    this.showNotification(data.error, 'error');
                }
            } catch (error) {
                this.showNotification('清空消息记录失败', 'error');
            }
        }
    }

    filterMessages(type) {
        // 实现消息类型过滤
        const messageItems = document.querySelectorAll('.message-item');
        messageItems.forEach(item => {
            if (type === 'ALL' || item.classList.contains(type.toLowerCase())) {
                item.style.display = 'block';
            } else {
                item.style.display = 'none';
            }
        });
    }

    async resetStatistics() {
        if (confirm('确定要重置统计数据吗？')) {
            try {
                const response = await fetch('/api/statistics/reset', { method: 'POST' });
                const data = await response.json();

                if (data.success) {
                    this.showNotification('统计数据已重置', 'success');
                } else {
                    this.showNotification(data.error, 'error');
                }
            } catch (error) {
                this.showNotification('重置统计数据失败', 'error');
            }
        }
    }
    
    // 工具方法
    getStatusText(status) {
        const statusMap = {
            'running': '运行中',
            'stopped': '已停止',
            'paused': '已暂停',
            'starting': '启动中',
            'stopping': '停止中'
        };
        return statusMap[status] || '未知';
    }
    
    formatDuration(seconds) {
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const secs = seconds % 60;
        
        if (hours > 0) {
            return `${hours}小时${minutes}分${secs}秒`;
        } else if (minutes > 0) {
            return `${minutes}分${secs}秒`;
        } else {
            return `${secs}秒`;
        }
    }
    
    updateControlButtons(status) {
        const startBtn = document.getElementById('startBtn');
        const stopBtn = document.getElementById('stopBtn');
        const pauseBtn = document.getElementById('pauseBtn');
        const resumeBtn = document.getElementById('resumeBtn');
        
        // 重置所有按钮
        [startBtn, stopBtn, pauseBtn, resumeBtn].forEach(btn => {
            btn.disabled = false;
            btn.classList.remove('d-none');
        });
        
        switch (status) {
            case 'stopped':
                stopBtn.disabled = true;
                pauseBtn.disabled = true;
                resumeBtn.disabled = true;
                break;
            case 'running':
                startBtn.disabled = true;
                resumeBtn.disabled = true;
                break;
            case 'paused':
                startBtn.disabled = true;
                pauseBtn.disabled = true;
                break;
            case 'starting':
            case 'stopping':
                [startBtn, stopBtn, pauseBtn, resumeBtn].forEach(btn => {
                    btn.disabled = true;
                });
                break;
        }
    }
    
    setButtonLoading(buttonId, loading) {
        const button = document.getElementById(buttonId);
        if (loading) {
            button.disabled = true;
            button.innerHTML = '<span class="loading"></span> 处理中...';
        } else {
            button.disabled = false;
            // 恢复原始文本（这里需要根据按钮ID设置对应文本）
            const buttonTexts = {
                'startBtn': '启动',
                'stopBtn': '停止',
                'pauseBtn': '暂停',
                'resumeBtn': '恢复'
            };
            button.innerHTML = buttonTexts[buttonId] || '操作';
        }
    }
    
    updateConnectionStatus(connected) {
        const indicator = document.getElementById('connectionStatus');
        if (connected) {
            indicator.className = 'status-indicator status-running';
            indicator.textContent = '已连接';
        } else {
            indicator.className = 'status-indicator status-stopped';
            indicator.textContent = '连接断开';
        }
    }
    
    showNotification(message, type = 'info') {
        // 创建通知元素
        const notification = document.createElement('div');
        notification.className = `notification ${type} fade-in`;
        notification.textContent = message;
        
        // 添加到页面
        document.body.appendChild(notification);
        
        // 自动移除
        setTimeout(() => {
            notification.style.opacity = '0';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, 3000);
    }
    
    startPeriodicUpdates() {
        // 每30秒更新一次统计数据
        setInterval(() => {
            if (this.isConnected) {
                this.loadStatistics();
            }
        }, 30000);
        
        // 每分钟更新一次运行时长
        setInterval(() => {
            if (this.currentStatus === 'running') {
                this.updateUptime();
            }
        }, 60000);
    }
    
    async loadStatistics() {
        try {
            const response = await fetch('/api/statistics');
            if (response.ok) {
                const data = await response.json();
                this.updateStatistics(data.data);
            }
        } catch (error) {
            console.error('加载统计数据失败:', error);
        }
    }
    
    updateUptime() {
        // 这里可以实现客户端的运行时长计算
        // 或者通过WebSocket实时更新
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    window.botController = new BotController();
});
