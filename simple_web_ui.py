# 简化的Web UI启动文件
import os
import sys
import logging
from flask import Flask, render_template, jsonify, request
from flask_socketio import SocketIO

# 设置基础日志
logging.basicConfig(
    level=logging.INFO,
    format='[%(levelname)s][%(asctime)s] - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger(__name__)

def create_simple_app():
    """创建简化的Flask应用"""
    app = Flask(__name__, 
                template_folder='web_ui/templates',
                static_folder='web_ui/static')
    app.config['SECRET_KEY'] = 'dify-on-wechat-secret-key-default'
    
    # 初始化SocketIO
    socketio = SocketIO(app, cors_allowed_origins="*", async_mode='threading')
    
    # 模拟数据
    bot_status = {
        'status': 'stopped',
        'uptime': 0,
        'message_received': 0,
        'message_replied': 0,
        'success_rate': 0.0,
        'error_count': 0
    }
    
    config_data = {
        'use_deepseek': False,
        'deepseek_api_key': '',
        'deepseek_api_base': 'https://api.deepseek.com/v1',
        'auto_reply_text': '我已经下班，有问题明天再说，急事请电联',
        'enable_group_chat': True,
        'enable_single_chat': True
    }
    
    @app.route('/')
    def index():
        """主页面"""
        return render_template('index.html')
    
    @app.route('/api/status', methods=['GET'])
    def get_status():
        """获取机器人状态"""
        return jsonify({
            'success': True,
            'data': bot_status
        })
    
    @app.route('/api/start', methods=['POST'])
    def start_bot():
        """启动机器人"""
        bot_status['status'] = 'running'
        logger.info("机器人启动")
        return jsonify({
            'success': True,
            'message': '机器人启动成功'
        })
    
    @app.route('/api/stop', methods=['POST'])
    def stop_bot():
        """停止机器人"""
        bot_status['status'] = 'stopped'
        logger.info("机器人停止")
        return jsonify({
            'success': True,
            'message': '机器人停止成功'
        })
    
    @app.route('/api/pause', methods=['POST'])
    def pause_bot():
        """暂停机器人"""
        bot_status['status'] = 'paused'
        logger.info("机器人暂停")
        return jsonify({
            'success': True,
            'message': '机器人暂停成功'
        })
    
    @app.route('/api/resume', methods=['POST'])
    def resume_bot():
        """恢复机器人"""
        bot_status['status'] = 'running'
        logger.info("机器人恢复")
        return jsonify({
            'success': True,
            'message': '机器人恢复成功'
        })
    
    @app.route('/api/config', methods=['GET'])
    def get_config():
        """获取配置"""
        return jsonify({
            'success': True,
            'data': config_data
        })
    
    @app.route('/api/config', methods=['PUT'])
    def update_config():
        """更新配置"""
        try:
            new_config = request.get_json()
            if new_config:
                config_data.update(new_config)
                logger.info(f"配置已更新: {new_config}")
                return jsonify({
                    'success': True,
                    'message': '配置更新成功'
                })
            else:
                return jsonify({
                    'success': False,
                    'error': '无效的配置数据'
                }), 400
        except Exception as e:
            logger.error(f"更新配置失败: {e}")
            return jsonify({
                'success': False,
                'error': str(e)
            }), 500
    
    @app.route('/api/statistics', methods=['GET'])
    def get_statistics():
        """获取统计数据"""
        return jsonify({
            'success': True,
            'data': bot_status
        })
    
    @app.route('/api/logs', methods=['GET'])
    def get_logs():
        """获取日志"""
        # 模拟日志数据
        logs = [
            {
                'level': 'INFO',
                'message': 'Web UI启动成功',
                'timestamp': '2024-01-01T12:00:00',
                'formatted_time': '2024-01-01 12:00:00'
            },
            {
                'level': 'INFO', 
                'message': '等待机器人连接...',
                'timestamp': '2024-01-01T12:00:01',
                'formatted_time': '2024-01-01 12:00:01'
            }
        ]
        return jsonify({
            'success': True,
            'data': logs
        })
    
    @app.route('/api/messages', methods=['GET'])
    def get_messages():
        """获取消息记录"""
        # 模拟消息数据
        messages = [
            {
                'type': 'received',
                'content': '测试消息',
                'sender': '测试用户',
                'timestamp': '2024-01-01T12:00:00',
                'formatted_time': '2024-01-01 12:00:00'
            }
        ]
        return jsonify({
            'success': True,
            'data': messages
        })
    
    @socketio.on('connect')
    def handle_connect():
        """客户端连接"""
        logger.info("WebSocket客户端连接")
        socketio.emit('status_update', bot_status)
    
    @socketio.on('disconnect')
    def handle_disconnect():
        """客户端断开连接"""
        logger.info("WebSocket客户端断开连接")
    
    return app, socketio

def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description='简化版Web UI')
    parser.add_argument('--host', default='127.0.0.1', help='监听地址')
    parser.add_argument('--port', type=int, default=7860, help='监听端口')

    args = parser.parse_args()

    try:
        logger.info("启动简化版Web UI...")

        app, socketio = create_simple_app()

        host = args.host
        port = args.port

        logger.info(f"Web UI服务启动成功")
        logger.info(f"访问地址: http://{host}:{port}")
        logger.info("按 Ctrl+C 停止服务")

        socketio.run(
            app,
            host=host,
            port=port,
            debug=False,
            allow_unsafe_werkzeug=True
        )
        
    except Exception as e:
        logger.error(f"启动失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()
