# DeepSeek AI机器人
import os
import sys
import json
import time
import requests
from typing import Dict, Any, Optional

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from bot.bot import Bo<PERSON>
from bridge.context import Context, ContextType
from bridge.reply import Reply, ReplyType
from common.log import logger
from config import conf

class DeepSeekBot(Bot):
    """DeepSeek AI机器人"""
    
    def __init__(self):
        super().__init__()
        self.api_key = ""
        self.api_base = "https://api.deepseek.com/v1"
        self.model = "deepseek-chat"
        self.max_tokens = 2000
        self.temperature = 0.7
        self.timeout = 30
        
        # 加载配置
        self._load_config()
        
        logger.info("DeepSeek AI机器人初始化完成")
    
    def _load_config(self):
        """加载配置"""
        try:
            self.api_key = conf().get("deepseek_api_key", "")
            self.api_base = conf().get("deepseek_api_base", "https://api.deepseek.com/v1")
            self.model = conf().get("deepseek_model", "deepseek-chat")
            self.max_tokens = conf().get("deepseek_max_tokens", 2000)
            self.temperature = conf().get("deepseek_temperature", 0.7)
            self.timeout = conf().get("deepseek_timeout", 30)
            
            if not self.api_key:
                logger.warning("DeepSeek API Key未配置")
            else:
                logger.info("DeepSeek配置加载成功")
                
        except Exception as e:
            logger.error(f"加载DeepSeek配置失败: {e}")
    
    def validate_api_key(self) -> bool:
        """验证API Key"""
        if not self.api_key:
            return False
        
        try:
            # 发送一个简单的测试请求
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }
            
            data = {
                "model": self.model,
                "messages": [
                    {"role": "user", "content": "Hello"}
                ],
                "max_tokens": 10
            }
            
            response = requests.post(
                f"{self.api_base}/chat/completions",
                headers=headers,
                json=data,
                timeout=10
            )
            
            return response.status_code == 200
            
        except Exception as e:
            logger.error(f"验证API Key失败: {e}")
            return False
    
    def get_model_info(self) -> Dict[str, Any]:
        """获取模型信息"""
        try:
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }
            
            response = requests.get(
                f"{self.api_base}/models",
                headers=headers,
                timeout=self.timeout
            )
            
            if response.status_code == 200:
                return response.json()
            else:
                logger.error(f"获取模型信息失败: {response.status_code}")
                return {}
                
        except Exception as e:
            logger.error(f"获取模型信息异常: {e}")
            return {}
    
    def reply(self, query: str, context: Context = None) -> Reply:
        """AI回复"""
        try:
            if not self.api_key:
                logger.error("DeepSeek API Key未配置")
                return Reply(ReplyType.ERROR, "AI服务未配置")
            
            if context and context.type == ContextType.TEXT:
                logger.info(f"[DeepSeekBot] 收到消息: {query}")
                
                # 构建消息历史
                messages = self._build_messages(query, context)
                
                # 调用DeepSeek API
                response_text = self._call_deepseek_api(messages)
                
                if response_text:
                    logger.info(f"[DeepSeekBot] AI回复: {response_text}")
                    return Reply(ReplyType.TEXT, response_text)
                else:
                    logger.error("DeepSeek API返回空响应")
                    return Reply(ReplyType.ERROR, "AI服务暂时不可用")
            else:
                return Reply(ReplyType.ERROR, "不支持的消息类型")
                
        except Exception as e:
            logger.error(f"[DeepSeekBot] 回复失败: {e}")
            return Reply(ReplyType.ERROR, "AI回复失败")
    
    def _build_messages(self, query: str, context: Context = None) -> list:
        """构建消息历史"""
        messages = []
        
        # 系统提示词
        system_prompt = self._get_system_prompt(context)
        if system_prompt:
            messages.append({"role": "system", "content": system_prompt})
        
        # 用户消息
        messages.append({"role": "user", "content": query})
        
        return messages
    
    def _get_system_prompt(self, context: Context = None) -> str:
        """获取系统提示词"""
        default_prompt = """你是一个智能助手，请用友好、专业的语气回答用户的问题。
请注意：
1. 回答要简洁明了，避免过于冗长
2. 如果不确定答案，请诚实说明
3. 保持礼貌和专业的语气
4. 根据上下文提供相关的帮助"""
        
        # 可以根据配置或上下文自定义系统提示词
        custom_prompt = conf().get("deepseek_system_prompt", "")
        
        return custom_prompt if custom_prompt else default_prompt
    
    def _call_deepseek_api(self, messages: list) -> Optional[str]:
        """调用DeepSeek API"""
        try:
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }
            
            data = {
                "model": self.model,
                "messages": messages,
                "max_tokens": self.max_tokens,
                "temperature": self.temperature,
                "stream": False
            }
            
            logger.debug(f"调用DeepSeek API: {self.api_base}/chat/completions")
            
            response = requests.post(
                f"{self.api_base}/chat/completions",
                headers=headers,
                json=data,
                timeout=self.timeout
            )
            
            if response.status_code == 200:
                result = response.json()
                
                if "choices" in result and len(result["choices"]) > 0:
                    content = result["choices"][0]["message"]["content"]
                    return content.strip()
                else:
                    logger.error("DeepSeek API响应格式异常")
                    return None
            else:
                logger.error(f"DeepSeek API请求失败: {response.status_code}, {response.text}")
                return None
                
        except requests.exceptions.Timeout:
            logger.error("DeepSeek API请求超时")
            return None
        except requests.exceptions.RequestException as e:
            logger.error(f"DeepSeek API请求异常: {e}")
            return None
        except json.JSONDecodeError as e:
            logger.error(f"DeepSeek API响应解析失败: {e}")
            return None
        except Exception as e:
            logger.error(f"调用DeepSeek API异常: {e}")
            return None
    
    def reply_text(self, session, query, context=None):
        """文本消息回复"""
        return self.reply(query, context)
    
    def test_connection(self) -> Dict[str, Any]:
        """测试连接"""
        try:
            start_time = time.time()
            
            # 发送测试消息
            test_messages = [
                {"role": "user", "content": "Hello, this is a test message."}
            ]
            
            response = self._call_deepseek_api(test_messages)
            
            end_time = time.time()
            response_time = round((end_time - start_time) * 1000, 2)  # 毫秒
            
            if response:
                return {
                    "success": True,
                    "response_time": response_time,
                    "response": response,
                    "message": "连接测试成功"
                }
            else:
                return {
                    "success": False,
                    "response_time": response_time,
                    "message": "连接测试失败"
                }
                
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "message": "连接测试异常"
            }
    
    def get_usage_info(self) -> Dict[str, Any]:
        """获取使用信息"""
        # DeepSeek API可能不提供使用量查询，这里返回基本信息
        return {
            "model": self.model,
            "api_base": self.api_base,
            "max_tokens": self.max_tokens,
            "temperature": self.temperature,
            "timeout": self.timeout,
            "api_key_configured": bool(self.api_key)
        }
    
    def update_config(self, config: Dict[str, Any]):
        """更新配置"""
        try:
            if "deepseek_api_key" in config:
                self.api_key = config["deepseek_api_key"]
            
            if "deepseek_api_base" in config:
                self.api_base = config["deepseek_api_base"]
            
            if "deepseek_model" in config:
                self.model = config["deepseek_model"]
            
            if "deepseek_max_tokens" in config:
                self.max_tokens = config["deepseek_max_tokens"]
            
            if "deepseek_temperature" in config:
                self.temperature = config["deepseek_temperature"]
            
            if "deepseek_timeout" in config:
                self.timeout = config["deepseek_timeout"]
            
            logger.info("DeepSeek配置更新成功")
            return True
            
        except Exception as e:
            logger.error(f"更新DeepSeek配置失败: {e}")
            return False
