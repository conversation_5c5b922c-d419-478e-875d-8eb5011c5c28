# 配置管理器
import os
import sys
import json
import threading
from typing import Dict, Any, Tuple, List

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config import conf
from common.log import logger

class ConfigValidator:
    """配置验证器"""
    
    @staticmethod
    def validate_deepseek_config(config: Dict[str, Any]) -> Tu<PERSON>[bool, List[str]]:
        """验证DeepSeek配置"""
        errors = []
        
        if config.get('use_deepseek', False):
            api_key = config.get('deepseek_api_key', '').strip()
            if not api_key:
                errors.append("DeepSeek API Key不能为空")
            elif not api_key.startswith('sk-'):
                errors.append("DeepSeek API Key格式不正确，应以'sk-'开头")
            
            api_base = config.get('deepseek_api_base', '').strip()
            if api_base and not api_base.startswith(('http://', 'https://')):
                errors.append("DeepSeek API Base URL格式不正确")
        
        return len(errors) == 0, errors
    
    @staticmethod
    def validate_message_config(config: Dict[str, Any]) -> Tuple[bool, List[str]]:
        """验证消息配置"""
        errors = []
        
        # 验证自定义回复文本
        auto_reply_text = config.get('auto_reply_text', '').strip()
        if not auto_reply_text:
            errors.append("自定义回复文本不能为空")
        elif len(auto_reply_text) > 500:
            errors.append("自定义回复文本长度不能超过500字符")
        
        return len(errors) == 0, errors
    
    @staticmethod
    def validate_channel_config(config: Dict[str, Any]) -> Tuple[bool, List[str]]:
        """验证通道配置"""
        errors = []
        
        channel_type = config.get('channel_type', '').strip()
        if not channel_type:
            errors.append("通道类型不能为空")
        elif channel_type not in ['wework', 'wx', 'terminal']:
            errors.append("不支持的通道类型")
        
        return len(errors) == 0, errors
    
    @classmethod
    def validate_all(cls, config: Dict[str, Any]) -> Tuple[bool, List[str]]:
        """验证所有配置"""
        all_errors = []
        
        # 验证DeepSeek配置
        is_valid, errors = cls.validate_deepseek_config(config)
        all_errors.extend(errors)
        
        # 验证消息配置
        is_valid, errors = cls.validate_message_config(config)
        all_errors.extend(errors)
        
        # 验证通道配置
        is_valid, errors = cls.validate_channel_config(config)
        all_errors.extend(errors)
        
        return len(all_errors) == 0, all_errors

class ConfigManager:
    """配置管理器，支持实时配置修改"""
    
    def __init__(self):
        self.config_file = "config.json"
        self.config_cache = {}
        self.lock = threading.Lock()
        self.validator = ConfigValidator()
        
        # 加载初始配置
        self._load_config()
        
        logger.info("配置管理器初始化完成")
    
    def _load_config(self):
        """加载配置文件"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    self.config_cache = json.load(f)
                logger.info("配置文件加载成功")
            else:
                logger.warning("配置文件不存在，使用默认配置")
                self.config_cache = self._get_default_config()
        except Exception as e:
            logger.error(f"加载配置文件失败: {e}")
            self.config_cache = self._get_default_config()
    
    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            # 基础配置
            "channel_type": "wework",
            "debug": True,
            
            # AI配置
            "use_deepseek": False,
            "deepseek_api_key": "",
            "deepseek_api_base": "https://api.deepseek.com/v1",
            "deepseek_model": "deepseek-chat",
            
            # 回复配置
            "auto_reply_text": "我已经下班，有问题明天再说，急事请电联",
            
            # 消息控制
            "enable_group_chat": True,
            "enable_single_chat": True,
            
            # 群聊配置
            "group_chat_prefix": ["@bot"],
            "group_name_white_list": ["ALL_GROUP"],
            
            # 单聊配置
            "single_chat_prefix": [""],
            "single_chat_reply_prefix": "",
            
            # 其他配置
            "nick_name_black_list": [],
            "speech_recognition": False,
            "voice_reply_voice": False,
            "image_recognition": False
        }
    
    def get_config(self) -> Dict[str, Any]:
        """获取当前配置"""
        with self.lock:
            # 合并当前配置和默认配置，确保所有必要字段都存在
            default_config = self._get_default_config()
            merged_config = default_config.copy()
            merged_config.update(self.config_cache)
            return merged_config.copy()
    
    def update_config(self, new_config: Dict[str, Any]) -> bool:
        """更新配置"""
        with self.lock:
            try:
                # 验证配置
                is_valid, errors = self.validator.validate_all(new_config)
                if not is_valid:
                    logger.error(f"配置验证失败: {errors}")
                    return False
                
                # 备份当前配置
                backup_config = self.config_cache.copy()
                
                # 更新配置缓存
                self.config_cache.update(new_config)
                
                # 保存到文件
                if self._save_config():
                    # 重新加载全局配置
                    self._reload_global_config()
                    logger.info("配置更新成功")
                    return True
                else:
                    # 恢复备份配置
                    self.config_cache = backup_config
                    logger.error("配置保存失败，已恢复原配置")
                    return False
                    
            except Exception as e:
                logger.error(f"更新配置失败: {e}")
                return False
    
    def _save_config(self) -> bool:
        """保存配置到文件"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config_cache, f, ensure_ascii=False, indent=4)
            return True
        except Exception as e:
            logger.error(f"保存配置文件失败: {e}")
            return False
    
    def _reload_global_config(self):
        """重新加载全局配置"""
        try:
            # 重新加载config模块的配置
            from config import load_config
            load_config()
            logger.info("全局配置重新加载成功")
        except Exception as e:
            logger.error(f"重新加载全局配置失败: {e}")
    
    def validate_config(self, config_data: Dict[str, Any]) -> Tuple[bool, List[str]]:
        """验证配置数据"""
        return self.validator.validate_all(config_data)
    
    def get_config_field(self, field_name: str, default_value: Any = None) -> Any:
        """获取特定配置字段"""
        with self.lock:
            return self.config_cache.get(field_name, default_value)
    
    def set_config_field(self, field_name: str, value: Any) -> bool:
        """设置特定配置字段"""
        return self.update_config({field_name: value})
    
    def reset_config(self) -> bool:
        """重置配置为默认值"""
        with self.lock:
            try:
                self.config_cache = self._get_default_config()
                if self._save_config():
                    self._reload_global_config()
                    logger.info("配置已重置为默认值")
                    return True
                else:
                    logger.error("重置配置失败")
                    return False
            except Exception as e:
                logger.error(f"重置配置失败: {e}")
                return False
    
    def export_config(self) -> str:
        """导出配置为JSON字符串"""
        with self.lock:
            try:
                return json.dumps(self.config_cache, ensure_ascii=False, indent=4)
            except Exception as e:
                logger.error(f"导出配置失败: {e}")
                return ""
    
    def import_config(self, config_json: str) -> bool:
        """从JSON字符串导入配置"""
        try:
            config_data = json.loads(config_json)
            return self.update_config(config_data)
        except json.JSONDecodeError as e:
            logger.error(f"配置JSON格式错误: {e}")
            return False
        except Exception as e:
            logger.error(f"导入配置失败: {e}")
            return False
    
    def get_web_ui_config(self) -> Dict[str, Any]:
        """获取Web UI相关的配置"""
        config = self.get_config()
        
        # 只返回Web UI需要的配置字段
        web_ui_config = {
            # AI配置
            "use_deepseek": config.get("use_deepseek", False),
            "deepseek_api_key": config.get("deepseek_api_key", ""),
            "deepseek_api_base": config.get("deepseek_api_base", "https://api.deepseek.com/v1"),
            "deepseek_model": config.get("deepseek_model", "deepseek-chat"),
            
            # 回复配置
            "auto_reply_text": config.get("auto_reply_text", ""),
            
            # 消息控制
            "enable_group_chat": config.get("enable_group_chat", True),
            "enable_single_chat": config.get("enable_single_chat", True),
            
            # 基础配置
            "channel_type": config.get("channel_type", "wework"),
            "debug": config.get("debug", True)
        }
        
        return web_ui_config
