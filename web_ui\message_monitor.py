# 消息监控器
import os
import sys
import threading
from datetime import datetime
from typing import List, Dict, Any, Optional
from collections import deque
from enum import Enum

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from common.log import logger

class MessageType(Enum):
    """消息类型枚举"""
    RECEIVED = "received"
    SENT = "sent"
    ERROR = "error"

class MessageRecord:
    """消息记录类"""
    
    def __init__(self, 
                 message_type: MessageType,
                 content: str,
                 sender: str = "",
                 receiver: str = "",
                 chat_type: str = "unknown",
                 timestamp: datetime = None,
                 extra_info: Dict[str, Any] = None):
        self.message_type = message_type
        self.content = content
        self.sender = sender
        self.receiver = receiver
        self.chat_type = chat_type  # single, group
        self.timestamp = timestamp or datetime.now()
        self.extra_info = extra_info or {}
        self.id = self._generate_id()
    
    def _generate_id(self) -> str:
        """生成消息ID"""
        return f"{self.timestamp.strftime('%Y%m%d%H%M%S')}{id(self) % 10000:04d}"
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'id': self.id,
            'type': self.message_type.value,
            'content': self.content,
            'sender': self.sender,
            'receiver': self.receiver,
            'chat_type': self.chat_type,
            'timestamp': self.timestamp.isoformat(),
            'formatted_time': self.timestamp.strftime('%Y-%m-%d %H:%M:%S'),
            'extra_info': self.extra_info
        }

class MessageMonitor:
    """消息监控器，监控和记录消息收发"""
    
    def __init__(self, max_messages: int = 500):
        self.max_messages = max_messages
        self.messages = deque(maxlen=max_messages)
        self.lock = threading.Lock()
        self.websocket_manager = None
        
        # 统计计数器
        self.total_received = 0
        self.total_sent = 0
        self.total_errors = 0
        
        logger.info("消息监控器初始化完成")
    
    def set_websocket_manager(self, websocket_manager):
        """设置WebSocket管理器"""
        self.websocket_manager = websocket_manager
    
    def record_received_message(self, 
                              content: str,
                              sender: str = "",
                              receiver: str = "",
                              chat_type: str = "unknown",
                              extra_info: Dict[str, Any] = None):
        """记录接收到的消息"""
        message_record = MessageRecord(
            message_type=MessageType.RECEIVED,
            content=content,
            sender=sender,
            receiver=receiver,
            chat_type=chat_type,
            extra_info=extra_info
        )
        
        with self.lock:
            self.messages.append(message_record)
            self.total_received += 1
        
        # 实时推送到WebSocket客户端
        if self.websocket_manager:
            self.websocket_manager.broadcast_message_received(message_record.to_dict())
        
        logger.debug(f"记录接收消息: {content[:50]}...")
    
    def record_sent_message(self,
                          content: str,
                          sender: str = "",
                          receiver: str = "",
                          chat_type: str = "unknown",
                          extra_info: Dict[str, Any] = None):
        """记录发送的消息"""
        message_record = MessageRecord(
            message_type=MessageType.SENT,
            content=content,
            sender=sender,
            receiver=receiver,
            chat_type=chat_type,
            extra_info=extra_info
        )
        
        with self.lock:
            self.messages.append(message_record)
            self.total_sent += 1
        
        # 实时推送到WebSocket客户端
        if self.websocket_manager:
            self.websocket_manager.broadcast_message_sent(message_record.to_dict())
        
        logger.debug(f"记录发送消息: {content[:50]}...")
    
    def record_error_message(self,
                           content: str,
                           error_info: str = "",
                           extra_info: Dict[str, Any] = None):
        """记录错误消息"""
        if extra_info is None:
            extra_info = {}
        extra_info['error_info'] = error_info
        
        message_record = MessageRecord(
            message_type=MessageType.ERROR,
            content=content,
            extra_info=extra_info
        )
        
        with self.lock:
            self.messages.append(message_record)
            self.total_errors += 1
        
        # 实时推送到WebSocket客户端
        if self.websocket_manager:
            self.websocket_manager.broadcast_message_error(message_record.to_dict())
        
        logger.debug(f"记录错误消息: {content[:50]}...")
    
    def get_messages(self, 
                    limit: int = 50,
                    message_type: str = "ALL",
                    chat_type: str = "ALL") -> List[Dict[str, Any]]:
        """获取消息记录"""
        with self.lock:
            filtered_messages = list(self.messages)
            
            # 按消息类型过滤
            if message_type != "ALL":
                filtered_messages = [
                    msg for msg in filtered_messages
                    if msg.message_type.value == message_type.lower()
                ]
            
            # 按聊天类型过滤
            if chat_type != "ALL":
                filtered_messages = [
                    msg for msg in filtered_messages
                    if msg.chat_type == chat_type.lower()
                ]
            
            # 限制数量并转换为字典
            limited_messages = filtered_messages[-limit:] if limit > 0 else filtered_messages
            return [msg.to_dict() for msg in limited_messages]
    
    def get_received_messages(self, limit: int = 50) -> List[Dict[str, Any]]:
        """获取接收的消息"""
        return self.get_messages(limit=limit, message_type="received")
    
    def get_sent_messages(self, limit: int = 50) -> List[Dict[str, Any]]:
        """获取发送的消息"""
        return self.get_messages(limit=limit, message_type="sent")
    
    def get_error_messages(self, limit: int = 50) -> List[Dict[str, Any]]:
        """获取错误消息"""
        return self.get_messages(limit=limit, message_type="error")
    
    def get_group_messages(self, limit: int = 50) -> List[Dict[str, Any]]:
        """获取群聊消息"""
        return self.get_messages(limit=limit, chat_type="group")
    
    def get_single_messages(self, limit: int = 50) -> List[Dict[str, Any]]:
        """获取单聊消息"""
        return self.get_messages(limit=limit, chat_type="single")
    
    def search_messages(self, keyword: str, limit: int = 50) -> List[Dict[str, Any]]:
        """搜索消息"""
        with self.lock:
            keyword_lower = keyword.lower()
            filtered_messages = [
                msg for msg in self.messages
                if keyword_lower in msg.content.lower() or 
                   keyword_lower in msg.sender.lower() or
                   keyword_lower in msg.receiver.lower()
            ]
            
            limited_messages = filtered_messages[-limit:] if limit > 0 else filtered_messages
            return [msg.to_dict() for msg in limited_messages]
    
    def get_recent_messages(self, minutes: int = 10) -> List[Dict[str, Any]]:
        """获取最近几分钟的消息"""
        with self.lock:
            cutoff_time = datetime.now().timestamp() - (minutes * 60)
            recent_messages = [
                msg for msg in self.messages
                if msg.timestamp.timestamp() >= cutoff_time
            ]
            return [msg.to_dict() for msg in recent_messages]
    
    def get_message_statistics(self) -> Dict[str, Any]:
        """获取消息统计信息"""
        with self.lock:
            stats = {
                'total_messages': len(self.messages),
                'total_received': self.total_received,
                'total_sent': self.total_sent,
                'total_errors': self.total_errors,
                'received_count': 0,
                'sent_count': 0,
                'error_count': 0,
                'group_count': 0,
                'single_count': 0
            }
            
            for msg in self.messages:
                if msg.message_type == MessageType.RECEIVED:
                    stats['received_count'] += 1
                elif msg.message_type == MessageType.SENT:
                    stats['sent_count'] += 1
                elif msg.message_type == MessageType.ERROR:
                    stats['error_count'] += 1
                
                if msg.chat_type == 'group':
                    stats['group_count'] += 1
                elif msg.chat_type == 'single':
                    stats['single_count'] += 1
            
            return stats
    
    def clear_messages(self):
        """清空消息记录"""
        with self.lock:
            self.messages.clear()
            logger.info("消息记录已清空")
    
    def reset_statistics(self):
        """重置统计数据"""
        with self.lock:
            self.total_received = 0
            self.total_sent = 0
            self.total_errors = 0
            logger.info("消息统计数据已重置")
    
    def export_messages(self, format: str = "json") -> str:
        """导出消息记录"""
        messages = self.get_messages(limit=0)  # 获取所有消息
        
        if format == "json":
            import json
            return json.dumps(messages, ensure_ascii=False, indent=2)
        elif format == "txt":
            lines = []
            for msg in messages:
                line = f"[{msg['formatted_time']}] [{msg['type'].upper()}] [{msg['chat_type']}] {msg['sender']} -> {msg['receiver']}: {msg['content']}"
                lines.append(line)
            return "\n".join(lines)
        elif format == "csv":
            import csv
            import io
            output = io.StringIO()
            writer = csv.writer(output)
            
            # 写入标题行
            writer.writerow(['时间', '类型', '聊天类型', '发送者', '接收者', '内容'])
            
            # 写入数据行
            for msg in messages:
                writer.writerow([
                    msg['formatted_time'],
                    msg['type'],
                    msg['chat_type'],
                    msg['sender'],
                    msg['receiver'],
                    msg['content']
                ])
            
            return output.getvalue()
        else:
            raise ValueError(f"不支持的导出格式: {format}")
    
    def get_conversation_history(self, user_id: str, limit: int = 50) -> List[Dict[str, Any]]:
        """获取与特定用户的对话历史"""
        with self.lock:
            conversation_messages = [
                msg for msg in self.messages
                if msg.sender == user_id or msg.receiver == user_id
            ]
            
            limited_messages = conversation_messages[-limit:] if limit > 0 else conversation_messages
            return [msg.to_dict() for msg in limited_messages]
    
    def get_active_users(self, hours: int = 24) -> List[Dict[str, Any]]:
        """获取活跃用户列表"""
        with self.lock:
            cutoff_time = datetime.now().timestamp() - (hours * 3600)
            user_stats = {}
            
            for msg in self.messages:
                if msg.timestamp.timestamp() >= cutoff_time:
                    user_id = msg.sender if msg.message_type == MessageType.RECEIVED else msg.receiver
                    if user_id and user_id not in user_stats:
                        user_stats[user_id] = {
                            'user_id': user_id,
                            'message_count': 0,
                            'last_message_time': msg.timestamp.isoformat()
                        }
                    
                    if user_id:
                        user_stats[user_id]['message_count'] += 1
                        if msg.timestamp > datetime.fromisoformat(user_stats[user_id]['last_message_time'].replace('Z', '+00:00')):
                            user_stats[user_id]['last_message_time'] = msg.timestamp.isoformat()
            
            return sorted(user_stats.values(), key=lambda x: x['message_count'], reverse=True)
