# 机器人控制器
import os
import sys
import time
import threading
from datetime import datetime
from enum import Enum

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from common.log import logger
from config import conf

class BotStatus(Enum):
    """机器人状态枚举"""
    STOPPED = "stopped"
    RUNNING = "running"
    PAUSED = "paused"
    STARTING = "starting"
    STOPPING = "stopping"

class ServiceThread(threading.Thread):
    """服务线程，包装现有的app.py逻辑"""
    
    def __init__(self, controller):
        super().__init__(daemon=True)
        self.controller = controller
        self.should_stop = threading.Event()
        self.is_paused = threading.Event()
        self.channel = None
        
    def run(self):
        """运行服务线程"""
        try:
            logger.info("服务线程启动")
            self.controller.status = BotStatus.RUNNING
            self.controller.start_time = datetime.now()
            
            # 导入并启动现有的机器人逻辑
            from app import start_channel
            from config import load_config
            
            # 加载配置
            load_config()
            
            # 获取通道类型
            channel_name = conf().get("channel_type", "wework")
            
            # 启动通道
            start_channel(channel_name)
            
            # 保持运行状态
            while not self.should_stop.is_set():
                if self.is_paused.is_set():
                    # 暂停状态，等待恢复
                    self.controller.status = BotStatus.PAUSED
                    self.is_paused.wait()
                    if not self.should_stop.is_set():
                        self.controller.status = BotStatus.RUNNING
                
                time.sleep(1)
                
        except Exception as e:
            logger.error(f"服务线程运行异常: {e}")
            self.controller.status = BotStatus.STOPPED
        finally:
            logger.info("服务线程结束")
            self.controller.status = BotStatus.STOPPED
    
    def stop(self):
        """停止服务线程"""
        self.should_stop.set()
        if self.is_paused.is_set():
            self.is_paused.set()  # 如果暂停中，先恢复再停止
    
    def pause(self):
        """暂停服务"""
        self.is_paused.set()
    
    def resume(self):
        """恢复服务"""
        self.is_paused.clear()

class BotController:
    """机器人控制器，管理机器人生命周期"""
    
    def __init__(self):
        self.status = BotStatus.STOPPED
        self.start_time = None
        self.service_thread = None
        self.lock = threading.Lock()
        
        # 统计数据
        self.message_received = 0
        self.message_replied = 0
        self.error_count = 0
        
        logger.info("机器人控制器初始化完成")
    
    def get_status(self):
        """获取机器人状态"""
        with self.lock:
            uptime = 0
            if self.start_time and self.status in [BotStatus.RUNNING, BotStatus.PAUSED]:
                uptime = int((datetime.now() - self.start_time).total_seconds())
            
            return {
                'status': self.status.value,
                'uptime': uptime,
                'start_time': self.start_time.isoformat() if self.start_time else None,
                'message_received': self.message_received,
                'message_replied': self.message_replied,
                'error_count': self.error_count,
                'success_rate': self.get_success_rate()
            }
    
    def get_success_rate(self):
        """计算成功率"""
        if self.message_received == 0:
            return 0.0
        return round((self.message_replied / self.message_received) * 100, 2)
    
    def start_bot(self):
        """启动机器人"""
        with self.lock:
            if self.status != BotStatus.STOPPED:
                logger.warning(f"机器人当前状态为 {self.status.value}，无法启动")
                return False
            
            try:
                logger.info("正在启动机器人...")
                self.status = BotStatus.STARTING
                
                # 创建并启动服务线程
                self.service_thread = ServiceThread(self)
                self.service_thread.start()
                
                # 等待启动完成
                timeout = 10  # 10秒超时
                start_wait = time.time()
                while self.status == BotStatus.STARTING and (time.time() - start_wait) < timeout:
                    time.sleep(0.1)
                
                if self.status == BotStatus.RUNNING:
                    logger.info("机器人启动成功")
                    return True
                else:
                    logger.error("机器人启动超时或失败")
                    return False
                    
            except Exception as e:
                logger.error(f"启动机器人失败: {e}")
                self.status = BotStatus.STOPPED
                return False
    
    def stop_bot(self):
        """停止机器人"""
        with self.lock:
            if self.status == BotStatus.STOPPED:
                logger.warning("机器人已经停止")
                return True
            
            try:
                logger.info("正在停止机器人...")
                self.status = BotStatus.STOPPING
                
                if self.service_thread and self.service_thread.is_alive():
                    self.service_thread.stop()
                    self.service_thread.join(timeout=5)
                
                self.status = BotStatus.STOPPED
                self.start_time = None
                self.service_thread = None
                
                logger.info("机器人停止成功")
                return True
                
            except Exception as e:
                logger.error(f"停止机器人失败: {e}")
                return False
    
    def pause_bot(self):
        """暂停机器人"""
        with self.lock:
            if self.status != BotStatus.RUNNING:
                logger.warning(f"机器人当前状态为 {self.status.value}，无法暂停")
                return False
            
            try:
                logger.info("正在暂停机器人...")
                
                if self.service_thread and self.service_thread.is_alive():
                    self.service_thread.pause()
                
                # 等待状态更新
                timeout = 5
                start_wait = time.time()
                while self.status != BotStatus.PAUSED and (time.time() - start_wait) < timeout:
                    time.sleep(0.1)
                
                if self.status == BotStatus.PAUSED:
                    logger.info("机器人暂停成功")
                    return True
                else:
                    logger.error("机器人暂停失败")
                    return False
                    
            except Exception as e:
                logger.error(f"暂停机器人失败: {e}")
                return False
    
    def resume_bot(self):
        """恢复机器人"""
        with self.lock:
            if self.status != BotStatus.PAUSED:
                logger.warning(f"机器人当前状态为 {self.status.value}，无法恢复")
                return False
            
            try:
                logger.info("正在恢复机器人...")
                
                if self.service_thread and self.service_thread.is_alive():
                    self.service_thread.resume()
                
                # 等待状态更新
                timeout = 5
                start_wait = time.time()
                while self.status != BotStatus.RUNNING and (time.time() - start_wait) < timeout:
                    time.sleep(0.1)
                
                if self.status == BotStatus.RUNNING:
                    logger.info("机器人恢复成功")
                    return True
                else:
                    logger.error("机器人恢复失败")
                    return False
                    
            except Exception as e:
                logger.error(f"恢复机器人失败: {e}")
                return False
    
    def increment_received(self):
        """增加接收消息计数"""
        with self.lock:
            self.message_received += 1
    
    def increment_replied(self):
        """增加回复消息计数"""
        with self.lock:
            self.message_replied += 1
    
    def increment_error(self):
        """增加错误计数"""
        with self.lock:
            self.error_count += 1
    
    def reset_statistics(self):
        """重置统计数据"""
        with self.lock:
            self.message_received = 0
            self.message_replied = 0
            self.error_count = 0
            logger.info("统计数据已重置")
    
    def get_statistics(self):
        """获取统计数据"""
        return {
            'message_received': self.message_received,
            'message_replied': self.message_replied,
            'error_count': self.error_count,
            'success_rate': self.get_success_rate()
        }
