# Web UI启动文件 - 独立版本
import os
import sys
import argparse
import logging

# 设置基础日志
logging.basicConfig(
    level=logging.INFO,
    format='[%(levelname)s][%(asctime)s] - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger(__name__)

# 直接使用独立版本，避免复杂的导入问题
logger.info("使用独立Web UI版本")

try:
    from standalone_web_ui import create_standalone_app as create_app
    logger.info("成功导入独立版Web UI")
except ImportError as e:
    logger.error(f"无法导入独立版Web UI: {e}")
    logger.error("请确保standalone_web_ui.py文件存在")
    sys.exit(1)

# 简单的配置类
class SimpleConf:
    def get(self, key, default=None):
        defaults = {
            'web_ui_host': '127.0.0.1',
            'web_ui_port': 7860,
            'debug': False
        }
        return defaults.get(key, default)

conf = SimpleConf()

def load_config():
    """空的配置加载函数"""
    pass

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='企业微信机器人Web UI')
    parser.add_argument('--host', default='0.0.0.0', help='监听地址')
    parser.add_argument('--port', type=int, default=7860, help='监听端口')
    parser.add_argument('--debug', action='store_true', help='调试模式')
    
    args = parser.parse_args()
    
    try:
        # 加载配置
        load_config()
        
        # 获取Web UI配置
        host = args.host or conf.get('web_ui_host', '127.0.0.1')
        port = args.port or conf.get('web_ui_port', 7860)
        debug = args.debug or conf.get('debug', False)
        
        logger.info(f"启动Web UI服务...")
        logger.info(f"监听地址: {host}:{port}")
        logger.info(f"调试模式: {debug}")
        
        # 创建Flask应用
        app, socketio = create_app()

        # 尝试设置日志管理器的WebSocket管理器（如果存在）
        try:
            if hasattr(app, 'log_manager') and hasattr(app, 'websocket_manager'):
                app.log_manager.set_websocket_manager(app.websocket_manager)
            if hasattr(app, 'message_monitor') and hasattr(app, 'websocket_manager'):
                app.message_monitor.set_websocket_manager(app.websocket_manager)
        except Exception as e:
            logger.warning(f"设置WebSocket管理器失败: {e}")
            logger.info("继续使用简化模式")
        
        logger.info("Web UI服务启动成功")
        logger.info(f"请访问: http://{host}:{port}")
        
        # 启动服务
        socketio.run(
            app,
            host=host,
            port=port,
            debug=debug,
            allow_unsafe_werkzeug=True
        )
        
    except KeyboardInterrupt:
        logger.info("收到中断信号，正在关闭Web UI服务...")
    except Exception as e:
        logger.error(f"Web UI服务启动失败: {e}")
        logger.exception(e)
        sys.exit(1)

if __name__ == '__main__':
    main()
