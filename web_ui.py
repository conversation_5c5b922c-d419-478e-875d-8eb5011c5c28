# Web UI启动文件
import os
import sys
import threading
import argparse
import logging
from flask import Flask

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 设置基础日志
logging.basicConfig(
    level=logging.INFO,
    format='[%(levelname)s][%(asctime)s][%(filename)s:%(lineno)d] - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger(__name__)

try:
    from config import conf, load_config
except ImportError as e:
    logger.error(f"导入config模块失败: {e}")
    logger.error("请确保配置文件正确")
    sys.exit(1)

try:
    from web_ui.app import create_app
except ImportError as e:
    logger.error(f"导入web_ui.app模块失败: {e}")
    logger.error("请确保所有依赖已正确安装")
    sys.exit(1)

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='企业微信机器人Web UI')
    parser.add_argument('--host', default='0.0.0.0', help='监听地址')
    parser.add_argument('--port', type=int, default=7860, help='监听端口')
    parser.add_argument('--debug', action='store_true', help='调试模式')
    
    args = parser.parse_args()
    
    try:
        # 加载配置
        load_config()
        
        # 获取Web UI配置
        host = args.host or conf().get('web_ui_host', '0.0.0.0')
        port = args.port or conf().get('web_ui_port', 7860)
        debug = args.debug or conf().get('debug', False)
        
        logger.info(f"启动Web UI服务...")
        logger.info(f"监听地址: {host}:{port}")
        logger.info(f"调试模式: {debug}")
        
        # 创建Flask应用
        app, socketio = create_app()
        
        # 设置日志管理器的WebSocket管理器
        app.log_manager.set_websocket_manager(app.websocket_manager)
        app.message_monitor.set_websocket_manager(app.websocket_manager)
        
        logger.info("Web UI服务启动成功")
        logger.info(f"请访问: http://{host}:{port}")
        
        # 启动服务
        socketio.run(
            app,
            host=host,
            port=port,
            debug=debug,
            allow_unsafe_werkzeug=True
        )
        
    except KeyboardInterrupt:
        logger.info("收到中断信号，正在关闭Web UI服务...")
    except Exception as e:
        logger.error(f"Web UI服务启动失败: {e}")
        logger.exception(e)
        sys.exit(1)

if __name__ == '__main__':
    main()
