// 图表管理JavaScript文件

class ChartManager {
    constructor() {
        this.statisticsChart = null;
        this.chartData = {
            labels: [],
            received: [],
            replied: [],
            errors: []
        };
        this.maxDataPoints = 20;
        
        this.initCharts();
    }
    
    initCharts() {
        this.initStatisticsChart();
    }
    
    initStatisticsChart() {
        const ctx = document.getElementById('statisticsChart');
        if (!ctx) return;
        
        this.statisticsChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: this.chartData.labels,
                datasets: [
                    {
                        label: '接收消息',
                        data: this.chartData.received,
                        borderColor: 'rgb(54, 162, 235)',
                        backgroundColor: 'rgba(54, 162, 235, 0.1)',
                        tension: 0.4,
                        fill: true
                    },
                    {
                        label: '回复消息',
                        data: this.chartData.replied,
                        borderColor: 'rgb(75, 192, 192)',
                        backgroundColor: 'rgba(75, 192, 192, 0.1)',
                        tension: 0.4,
                        fill: true
                    },
                    {
                        label: '错误数',
                        data: this.chartData.errors,
                        borderColor: 'rgb(255, 99, 132)',
                        backgroundColor: 'rgba(255, 99, 132, 0.1)',
                        tension: 0.4,
                        fill: true
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: true,
                        text: '消息统计趋势'
                    },
                    legend: {
                        display: true,
                        position: 'top'
                    }
                },
                scales: {
                    x: {
                        display: true,
                        title: {
                            display: true,
                            text: '时间'
                        }
                    },
                    y: {
                        display: true,
                        title: {
                            display: true,
                            text: '数量'
                        },
                        beginAtZero: true
                    }
                },
                interaction: {
                    intersect: false,
                    mode: 'index'
                },
                elements: {
                    point: {
                        radius: 4,
                        hoverRadius: 6
                    }
                }
            }
        });
    }
    
    updateStatistics(statsData) {
        if (!this.statisticsChart) return;
        
        // 添加新的数据点
        const now = new Date();
        const timeLabel = now.toLocaleTimeString('zh-CN', { 
            hour: '2-digit', 
            minute: '2-digit' 
        });
        
        this.chartData.labels.push(timeLabel);
        this.chartData.received.push(statsData.message_received || 0);
        this.chartData.replied.push(statsData.message_replied || 0);
        this.chartData.errors.push(statsData.error_count || 0);
        
        // 限制数据点数量
        if (this.chartData.labels.length > this.maxDataPoints) {
            this.chartData.labels.shift();
            this.chartData.received.shift();
            this.chartData.replied.shift();
            this.chartData.errors.shift();
        }
        
        // 更新图表
        this.statisticsChart.update('none');
    }
    
    resetChart() {
        if (!this.statisticsChart) return;
        
        this.chartData.labels = [];
        this.chartData.received = [];
        this.chartData.replied = [];
        this.chartData.errors = [];
        
        this.statisticsChart.update();
    }
    
    updateChartTheme(isDark = false) {
        if (!this.statisticsChart) return;
        
        const textColor = isDark ? '#ffffff' : '#333333';
        const gridColor = isDark ? '#444444' : '#e0e0e0';
        
        this.statisticsChart.options.plugins.legend.labels.color = textColor;
        this.statisticsChart.options.plugins.title.color = textColor;
        this.statisticsChart.options.scales.x.title.color = textColor;
        this.statisticsChart.options.scales.y.title.color = textColor;
        this.statisticsChart.options.scales.x.ticks.color = textColor;
        this.statisticsChart.options.scales.y.ticks.color = textColor;
        this.statisticsChart.options.scales.x.grid.color = gridColor;
        this.statisticsChart.options.scales.y.grid.color = gridColor;
        
        this.statisticsChart.update();
    }
    
    exportChart(format = 'png') {
        if (!this.statisticsChart) return null;
        
        return this.statisticsChart.toBase64Image(format, 1.0);
    }
    
    resizeChart() {
        if (this.statisticsChart) {
            this.statisticsChart.resize();
        }
    }
}

// 创建全局图表管理器实例
let chartManager = null;

// 页面加载完成后初始化图表
document.addEventListener('DOMContentLoaded', function() {
    chartManager = new ChartManager();
    
    // 将更新函数暴露给全局作用域
    window.updateCharts = function(statsData) {
        if (chartManager) {
            chartManager.updateStatistics(statsData);
        }
    };
    
    window.resetCharts = function() {
        if (chartManager) {
            chartManager.resetChart();
        }
    };
    
    // 监听窗口大小变化
    window.addEventListener('resize', function() {
        if (chartManager) {
            chartManager.resizeChart();
        }
    });
});

// 实时数据更新类
class RealTimeDataUpdater {
    constructor() {
        this.updateInterval = 30000; // 30秒更新一次
        this.intervalId = null;
        this.isRunning = false;
    }
    
    start() {
        if (this.isRunning) return;
        
        this.isRunning = true;
        this.intervalId = setInterval(() => {
            this.updateData();
        }, this.updateInterval);
        
        console.log('实时数据更新已启动');
    }
    
    stop() {
        if (!this.isRunning) return;
        
        this.isRunning = false;
        if (this.intervalId) {
            clearInterval(this.intervalId);
            this.intervalId = null;
        }
        
        console.log('实时数据更新已停止');
    }
    
    async updateData() {
        try {
            // 获取最新统计数据
            const response = await fetch('/api/statistics');
            if (response.ok) {
                const data = await response.json();
                
                // 更新图表
                if (window.updateCharts) {
                    window.updateCharts(data.data);
                }
                
                // 更新统计显示
                if (window.botController) {
                    window.botController.updateStatistics(data.data);
                }
            }
        } catch (error) {
            console.error('更新实时数据失败:', error);
        }
    }
    
    setUpdateInterval(interval) {
        this.updateInterval = interval;
        
        if (this.isRunning) {
            this.stop();
            this.start();
        }
    }
}

// 创建实时数据更新器
const realTimeUpdater = new RealTimeDataUpdater();

// 页面可见性变化处理
document.addEventListener('visibilitychange', function() {
    if (document.hidden) {
        // 页面隐藏时停止更新
        realTimeUpdater.stop();
    } else {
        // 页面显示时恢复更新
        realTimeUpdater.start();
    }
});

// 页面加载完成后启动实时更新
document.addEventListener('DOMContentLoaded', function() {
    // 延迟启动，等待其他组件初始化完成
    setTimeout(() => {
        realTimeUpdater.start();
    }, 2000);
});

// 页面卸载时清理
window.addEventListener('beforeunload', function() {
    realTimeUpdater.stop();
});

// 导出功能
window.exportChart = function(format = 'png') {
    if (chartManager) {
        const imageData = chartManager.exportChart(format);
        if (imageData) {
            // 创建下载链接
            const link = document.createElement('a');
            link.download = `statistics_chart_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.${format}`;
            link.href = imageData;
            link.click();
        }
    }
};

// 主题切换功能
window.switchChartTheme = function(isDark) {
    if (chartManager) {
        chartManager.updateChartTheme(isDark);
    }
};

// 图表配置更新功能
window.updateChartConfig = function(config) {
    if (chartManager && chartManager.statisticsChart) {
        Object.assign(chartManager.statisticsChart.options, config);
        chartManager.statisticsChart.update();
    }
};
