repos:
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.4.0
    hooks:
      - id: fix-byte-order-marker
      - id: check-case-conflict
      - id: check-merge-conflict
      - id: debug-statements
      - id: pretty-format-json
        types: [text]
        files: \.json(.template)?$
        args: [ --autofix , --no-ensure-ascii, --indent=2, --no-sort-keys]
      - id: trailing-whitespace
        exclude: '(\/|^)lib\/'
        args: [ --markdown-linebreak-ext=md ]
  - repo: https://github.com/PyCQA/isort
    rev: 5.12.0
    hooks:
      - id: isort
        exclude: '(\/|^)lib\/'
  - repo: https://github.com/psf/black
    rev: 23.3.0
    hooks:
      - id: black
        exclude: '(\/|^)lib\/'
  - repo: https://github.com/PyCQA/flake8
    rev: 6.0.0
    hooks:
      - id: flake8
        exclude: '(\/|^)lib\/'