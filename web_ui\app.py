# Flask Web应用主文件
import os
import sys
import threading
from flask import Flask, render_template, request, jsonify
from flask_socketio import Socket<PERSON>, emit
from werkzeug.security import check_password_hash, generate_password_hash

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config import conf
from common.log import logger
from .bot_controller import BotController
from .config_manager import ConfigManager
from .statistics_manager import StatisticsManager
from .log_manager import LogManager
from .message_monitor import MessageMonitor
from .websocket_manager import WebSocketManager

def create_app():
    """创建Flask应用"""
    app = Flask(__name__)
    # 使用默认密钥，避免配置文件缺少该项时报错
    try:
        secret_key = conf().get('web_ui_secret_key', 'dify-on-wechat-secret-key-default')
    except:
        secret_key = 'dify-on-wechat-secret-key-default'
    app.config['SECRET_KEY'] = secret_key
    
    # 初始化SocketIO
    socketio = SocketIO(app, cors_allowed_origins="*", async_mode='threading')
    
    # 初始化管理器
    bot_controller = BotController()
    config_manager = ConfigManager()
    statistics_manager = StatisticsManager()
    log_manager = LogManager()
    message_monitor = MessageMonitor()
    websocket_manager = WebSocketManager(socketio)
    
    # 将管理器注册到应用上下文
    app.bot_controller = bot_controller
    app.config_manager = config_manager
    app.statistics_manager = statistics_manager
    app.log_manager = log_manager
    app.message_monitor = message_monitor
    app.websocket_manager = websocket_manager
    
    # 注册路由
    register_routes(app)
    register_websocket_events(socketio, app)
    
    return app, socketio

def register_routes(app):
    """注册HTTP路由"""
    
    @app.route('/')
    def index():
        """主页面"""
        return render_template('index.html')
    
    # 机器人控制API
    @app.route('/api/status', methods=['GET'])
    def get_status():
        """获取机器人状态"""
        try:
            status = app.bot_controller.get_status()
            return jsonify({
                'success': True,
                'data': status
            })
        except Exception as e:
            logger.error(f"获取状态失败: {e}")
            return jsonify({
                'success': False,
                'error': str(e)
            }), 500
    
    @app.route('/api/start', methods=['POST'])
    def start_bot():
        """启动机器人"""
        try:
            result = app.bot_controller.start_bot()
            if result:
                app.websocket_manager.broadcast_status_update()
                return jsonify({
                    'success': True,
                    'message': '机器人启动成功'
                })
            else:
                return jsonify({
                    'success': False,
                    'error': '机器人启动失败'
                }), 500
        except Exception as e:
            logger.error(f"启动机器人失败: {e}")
            return jsonify({
                'success': False,
                'error': str(e)
            }), 500
    
    @app.route('/api/stop', methods=['POST'])
    def stop_bot():
        """停止机器人"""
        try:
            result = app.bot_controller.stop_bot()
            if result:
                app.websocket_manager.broadcast_status_update()
                return jsonify({
                    'success': True,
                    'message': '机器人停止成功'
                })
            else:
                return jsonify({
                    'success': False,
                    'error': '机器人停止失败'
                }), 500
        except Exception as e:
            logger.error(f"停止机器人失败: {e}")
            return jsonify({
                'success': False,
                'error': str(e)
            }), 500
    
    @app.route('/api/pause', methods=['POST'])
    def pause_bot():
        """暂停机器人"""
        try:
            result = app.bot_controller.pause_bot()
            if result:
                app.websocket_manager.broadcast_status_update()
                return jsonify({
                    'success': True,
                    'message': '机器人暂停成功'
                })
            else:
                return jsonify({
                    'success': False,
                    'error': '机器人暂停失败'
                }), 500
        except Exception as e:
            logger.error(f"暂停机器人失败: {e}")
            return jsonify({
                'success': False,
                'error': str(e)
            }), 500
    
    @app.route('/api/resume', methods=['POST'])
    def resume_bot():
        """恢复机器人"""
        try:
            result = app.bot_controller.resume_bot()
            if result:
                app.websocket_manager.broadcast_status_update()
                return jsonify({
                    'success': True,
                    'message': '机器人恢复成功'
                })
            else:
                return jsonify({
                    'success': False,
                    'error': '机器人恢复失败'
                }), 500
        except Exception as e:
            logger.error(f"恢复机器人失败: {e}")
            return jsonify({
                'success': False,
                'error': str(e)
            }), 500
    
    # 配置管理API
    @app.route('/api/config', methods=['GET'])
    def get_config():
        """获取配置"""
        try:
            config = app.config_manager.get_config()
            return jsonify({
                'success': True,
                'data': config
            })
        except Exception as e:
            logger.error(f"获取配置失败: {e}")
            return jsonify({
                'success': False,
                'error': str(e)
            }), 500
    
    @app.route('/api/config', methods=['PUT'])
    def update_config():
        """更新配置"""
        try:
            new_config = request.get_json()
            if not new_config:
                return jsonify({
                    'success': False,
                    'error': '无效的配置数据'
                }), 400
            
            result = app.config_manager.update_config(new_config)
            if result:
                app.websocket_manager.broadcast_config_changed()
                return jsonify({
                    'success': True,
                    'message': '配置更新成功'
                })
            else:
                return jsonify({
                    'success': False,
                    'error': '配置更新失败'
                }), 500
        except Exception as e:
            logger.error(f"更新配置失败: {e}")
            return jsonify({
                'success': False,
                'error': str(e)
            }), 500
    
    @app.route('/api/config/validate', methods=['POST'])
    def validate_config():
        """验证配置"""
        try:
            config_data = request.get_json()
            if not config_data:
                return jsonify({
                    'success': False,
                    'error': '无效的配置数据'
                }), 400
            
            is_valid, errors = app.config_manager.validate_config(config_data)
            return jsonify({
                'success': True,
                'data': {
                    'valid': is_valid,
                    'errors': errors
                }
            })
        except Exception as e:
            logger.error(f"验证配置失败: {e}")
            return jsonify({
                'success': False,
                'error': str(e)
            }), 500
    
    # 统计信息API
    @app.route('/api/statistics', methods=['GET'])
    def get_statistics():
        """获取统计数据"""
        try:
            stats = app.statistics_manager.get_statistics()
            return jsonify({
                'success': True,
                'data': stats
            })
        except Exception as e:
            logger.error(f"获取统计数据失败: {e}")
            return jsonify({
                'success': False,
                'error': str(e)
            }), 500
    
    @app.route('/api/statistics/reset', methods=['POST'])
    def reset_statistics():
        """重置统计数据"""
        try:
            app.statistics_manager.reset_statistics()
            app.websocket_manager.broadcast_statistics_update()
            return jsonify({
                'success': True,
                'message': '统计数据重置成功'
            })
        except Exception as e:
            logger.error(f"重置统计数据失败: {e}")
            return jsonify({
                'success': False,
                'error': str(e)
            }), 500
    
    # 日志管理API
    @app.route('/api/logs', methods=['GET'])
    def get_logs():
        """获取日志"""
        try:
            level = request.args.get('level', 'ALL')
            limit = int(request.args.get('limit', 100))
            search = request.args.get('search', '')
            
            logs = app.log_manager.get_logs(level=level, limit=limit, search=search)
            return jsonify({
                'success': True,
                'data': logs
            })
        except Exception as e:
            logger.error(f"获取日志失败: {e}")
            return jsonify({
                'success': False,
                'error': str(e)
            }), 500
    
    @app.route('/api/logs', methods=['DELETE'])
    def clear_logs():
        """清空日志"""
        try:
            app.log_manager.clear_logs()
            return jsonify({
                'success': True,
                'message': '日志清空成功'
            })
        except Exception as e:
            logger.error(f"清空日志失败: {e}")
            return jsonify({
                'success': False,
                'error': str(e)
            }), 500
    
    # 消息监控API
    @app.route('/api/messages', methods=['GET'])
    def get_messages():
        """获取消息记录"""
        try:
            limit = int(request.args.get('limit', 50))
            message_type = request.args.get('type', 'ALL')
            
            messages = app.message_monitor.get_messages(limit=limit, message_type=message_type)
            return jsonify({
                'success': True,
                'data': messages
            })
        except Exception as e:
            logger.error(f"获取消息记录失败: {e}")
            return jsonify({
                'success': False,
                'error': str(e)
            }), 500
    
    @app.route('/api/messages', methods=['DELETE'])
    def clear_messages():
        """清空消息记录"""
        try:
            app.message_monitor.clear_messages()
            return jsonify({
                'success': True,
                'message': '消息记录清空成功'
            })
        except Exception as e:
            logger.error(f"清空消息记录失败: {e}")
            return jsonify({
                'success': False,
                'error': str(e)
            }), 500

def register_websocket_events(socketio, app):
    """注册WebSocket事件"""
    
    @socketio.on('connect')
    def handle_connect():
        """客户端连接"""
        logger.info("WebSocket客户端连接")
        app.websocket_manager.add_connection(request.sid)
        
        # 发送初始状态
        emit('status_update', app.bot_controller.get_status())
        emit('statistics_update', app.statistics_manager.get_statistics())
    
    @socketio.on('disconnect')
    def handle_disconnect():
        """客户端断开连接"""
        logger.info("WebSocket客户端断开连接")
        app.websocket_manager.remove_connection(request.sid)
    
    @socketio.on('request_logs')
    def handle_request_logs(data):
        """请求日志数据"""
        try:
            level = data.get('level', 'ALL')
            limit = data.get('limit', 100)
            logs = app.log_manager.get_logs(level=level, limit=limit)
            emit('logs_data', logs)
        except Exception as e:
            logger.error(f"处理日志请求失败: {e}")
            emit('error', {'message': str(e)})
    
    @socketio.on('request_messages')
    def handle_request_messages(data):
        """请求消息数据"""
        try:
            limit = data.get('limit', 50)
            message_type = data.get('type', 'ALL')
            messages = app.message_monitor.get_messages(limit=limit, message_type=message_type)
            emit('messages_data', messages)
        except Exception as e:
            logger.error(f"处理消息请求失败: {e}")
            emit('error', {'message': str(e)})
