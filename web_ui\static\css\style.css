/* 企业微信机器人Web UI样式文件 */

/* 全局样式 */
:root {
    --primary-color: #007bff;
    --secondary-color: #6c757d;
    --success-color: #28a745;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    --border-radius: 8px;
    --box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    --transition: all 0.3s ease;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f5f5f5;
    color: #333;
    line-height: 1.6;
}

/* 导航栏样式 */
.navbar {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    box-shadow: var(--box-shadow);
}

.navbar-brand {
    font-weight: bold;
    font-size: 1.5rem;
}

/* 主容器 */
.main-container {
    padding: 20px 0;
    min-height: calc(100vh - 76px);
}

/* 卡片样式 */
.card {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    margin-bottom: 20px;
}

.card:hover {
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
    transform: translateY(-2px);
}

.card-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 1px solid #dee2e6;
    font-weight: 600;
    padding: 15px 20px;
}

.card-body {
    padding: 20px;
}

/* 状态指示器 */
.status-indicator {
    display: inline-flex;
    align-items: center;
    padding: 8px 16px;
    border-radius: 20px;
    font-weight: 500;
    font-size: 0.9rem;
    transition: var(--transition);
}

.status-running {
    background-color: rgba(40, 167, 69, 0.1);
    color: var(--success-color);
    border: 1px solid rgba(40, 167, 69, 0.3);
}

.status-stopped {
    background-color: rgba(220, 53, 69, 0.1);
    color: var(--danger-color);
    border: 1px solid rgba(220, 53, 69, 0.3);
}

.status-paused {
    background-color: rgba(255, 193, 7, 0.1);
    color: var(--warning-color);
    border: 1px solid rgba(255, 193, 7, 0.3);
}

.status-starting {
    background-color: rgba(23, 162, 184, 0.1);
    color: var(--info-color);
    border: 1px solid rgba(23, 162, 184, 0.3);
}

/* 按钮样式 */
.btn {
    border-radius: var(--border-radius);
    font-weight: 500;
    transition: var(--transition);
    border: none;
    padding: 10px 20px;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.btn-group .btn {
    margin-right: 10px;
}

.btn-group .btn:last-child {
    margin-right: 0;
}

/* 统计卡片 */
.stats-card {
    text-align: center;
    padding: 20px;
    background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
}

.stats-number {
    font-size: 2.5rem;
    font-weight: bold;
    color: var(--primary-color);
    margin-bottom: 5px;
}

.stats-label {
    color: var(--secondary-color);
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.stats-change {
    font-size: 0.8rem;
    margin-top: 5px;
}

.stats-change.positive {
    color: var(--success-color);
}

.stats-change.negative {
    color: var(--danger-color);
}

/* 日志面板 */
.log-container {
    background-color: #1e1e1e;
    color: #d4d4d4;
    border-radius: var(--border-radius);
    padding: 15px;
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    font-size: 0.85rem;
    line-height: 1.4;
    max-height: 400px;
    overflow-y: auto;
}

.log-entry {
    margin-bottom: 5px;
    padding: 2px 0;
    border-left: 3px solid transparent;
    padding-left: 8px;
}

.log-entry.debug {
    color: #9cdcfe;
    border-left-color: #9cdcfe;
}

.log-entry.info {
    color: #d4d4d4;
    border-left-color: #d4d4d4;
}

.log-entry.warning {
    color: #dcdcaa;
    border-left-color: #dcdcaa;
}

.log-entry.error {
    color: #f44747;
    border-left-color: #f44747;
}

.log-timestamp {
    color: #808080;
    margin-right: 8px;
}

.log-level {
    font-weight: bold;
    margin-right: 8px;
    padding: 1px 4px;
    border-radius: 3px;
    font-size: 0.75rem;
}

.log-level.debug {
    background-color: rgba(156, 220, 254, 0.2);
}

.log-level.info {
    background-color: rgba(212, 212, 212, 0.2);
}

.log-level.warning {
    background-color: rgba(220, 220, 170, 0.2);
}

.log-level.error {
    background-color: rgba(244, 71, 71, 0.2);
}

/* 消息面板 */
.message-container {
    max-height: 400px;
    overflow-y: auto;
    border: 1px solid #dee2e6;
    border-radius: var(--border-radius);
    padding: 10px;
}

.message-item {
    padding: 10px;
    margin-bottom: 10px;
    border-radius: var(--border-radius);
    border-left: 4px solid;
    background-color: #fff;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.message-item.received {
    border-left-color: var(--info-color);
    background-color: rgba(23, 162, 184, 0.05);
}

.message-item.sent {
    border-left-color: var(--success-color);
    background-color: rgba(40, 167, 69, 0.05);
}

.message-item.error {
    border-left-color: var(--danger-color);
    background-color: rgba(220, 53, 69, 0.05);
}

.message-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 5px;
    font-size: 0.85rem;
    color: var(--secondary-color);
}

.message-content {
    font-size: 0.9rem;
    line-height: 1.4;
}

/* 配置表单 */
.config-form .form-group {
    margin-bottom: 20px;
}

.config-form label {
    font-weight: 600;
    margin-bottom: 8px;
    display: block;
}

.config-form .form-control {
    border-radius: var(--border-radius);
    border: 1px solid #ced4da;
    transition: var(--transition);
}

.config-form .form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.config-section {
    background-color: #f8f9fa;
    padding: 20px;
    border-radius: var(--border-radius);
    margin-bottom: 20px;
}

.config-section h5 {
    color: var(--dark-color);
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 2px solid var(--primary-color);
}

/* 开关样式 */
.switch {
    position: relative;
    display: inline-block;
    width: 60px;
    height: 34px;
}

.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: var(--transition);
    border-radius: 34px;
}

.slider:before {
    position: absolute;
    content: "";
    height: 26px;
    width: 26px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: var(--transition);
    border-radius: 50%;
}

input:checked + .slider {
    background-color: var(--primary-color);
}

input:checked + .slider:before {
    transform: translateX(26px);
}

/* 工具栏 */
.toolbar {
    background-color: #fff;
    padding: 15px;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    margin-bottom: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
}

.toolbar .btn-group {
    margin: 5px 0;
}

.toolbar .form-control {
    max-width: 200px;
    margin: 5px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .main-container {
        padding: 10px 0;
    }
    
    .card-body {
        padding: 15px;
    }
    
    .stats-number {
        font-size: 2rem;
    }
    
    .toolbar {
        flex-direction: column;
        align-items: stretch;
    }
    
    .toolbar .btn-group {
        justify-content: center;
        margin: 10px 0;
    }
    
    .btn-group .btn {
        margin: 5px;
    }
}

/* 动画效果 */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.fade-in {
    animation: fadeIn 0.5s ease-out;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.pulse {
    animation: pulse 2s infinite;
}

/* 加载动画 */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255,255,255,.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* 通知样式 */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1050;
    max-width: 350px;
    padding: 15px;
    border-radius: var(--border-radius);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    transition: var(--transition);
}

.notification.success {
    background-color: var(--success-color);
    color: white;
}

.notification.error {
    background-color: var(--danger-color);
    color: white;
}

.notification.warning {
    background-color: var(--warning-color);
    color: #212529;
}

.notification.info {
    background-color: var(--info-color);
    color: white;
}
