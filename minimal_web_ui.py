# 最简化的Web UI启动文件
import logging
import argparse

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='[%(levelname)s][%(asctime)s] - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger(__name__)

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='最简化Web UI')
    parser.add_argument('--host', default='127.0.0.1', help='监听地址')
    parser.add_argument('--port', type=int, default=7860, help='监听端口')
    parser.add_argument('--debug', action='store_true', help='调试模式')
    
    args = parser.parse_args()
    
    try:
        logger.info("启动最简化Web UI...")
        
        # 直接导入并使用standalone_web_ui
        from standalone_web_ui import create_standalone_app
        
        app, socketio = create_standalone_app()
        
        logger.info(f"Web UI服务启动成功")
        logger.info(f"访问地址: http://{args.host}:{args.port}")
        logger.info("按 Ctrl+C 停止服务")
        
        # 启动服务
        socketio.run(
            app,
            host=args.host,
            port=args.port,
            debug=args.debug,
            allow_unsafe_werkzeug=True
        )
        
    except KeyboardInterrupt:
        logger.info("收到中断信号，正在关闭服务...")
    except Exception as e:
        logger.error(f"启动失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()
